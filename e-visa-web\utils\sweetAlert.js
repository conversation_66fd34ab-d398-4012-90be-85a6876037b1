import Swal from "sweetalert2";
import withReactContent from "sweetalert2-react-content";
export default function showSweetAlert(title, text, icon, timer = 0) {
  const MySwal = withReactContent(Swal);
  if (timer == 0) {
    return MySwal.fire({
      title: <strong>{title}</strong>,
      html: <i>{text}</i>,
      icon: icon,
      showClass: {
        popup: 'animate__animated animate__fadeInDown'
      },
      hideClass: {
        popup: 'animate__animated animate__fadeOutUp'
      }
    });
  } else {
    return MySwal.fire({
      title: <strong>{title}</strong>,
      html: <i>{text}</i>,
      icon: icon,
      timer: timer,
      timerProgressBar: true,
      showConfirmButton:false,
      showClass: {
        popup: 'animate__animated animate__fadeInDown'
      },
      hideClass: {
        popup: 'animate__animated animate__fadeOutUp'
      }
    });
  }
}
