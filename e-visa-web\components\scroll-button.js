import React, { useState } from "react";
import { UpOutlined } from "@ant-design/icons";
import { FloatButton } from "antd";
import useTranslation from "next-translate/useTranslation";

const ScrollButton = () => {
  const [visible, setVisible] = useState(false);
  const { t } = useTranslation();

  const toggleVisible = () => {
    const scrolled = document.documentElement.scrollTop;
    if (scrolled > 300) {
      setVisible(true);
    } else if (scrolled <= 300) {
      setVisible(false);
    }
  };

  const scrollToTop = () => {
    if (typeof window !== "undefined") {
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  };
  if (typeof window !== "undefined") {
    window.addEventListener("scroll", toggleVisible);
  }

  return (
    <FloatButton
      style={{ display: visible ? "inline" : "none" }}
      tooltip={t("site:backToTop")}
      icon={<UpOutlined />}
      onClick={scrollToTop}
    />
  );
};

export default ScrollButton;