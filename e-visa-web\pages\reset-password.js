import React from "react";
import { Button, Form, Input } from "antd";
import { useRouter } from "next/router";
import { useRef, useEffect, useState } from "react";
import { toast } from "react-toastify";
import useTranslation from "next-translate/useTranslation";
import showSweetAlert from "../utils/sweetAlert";
import axioshelper, { BaseUrlType, MethodType } from "../pages/api/axioshelper";
import UserEnter from "../components/userEnter";

const ResetPassword = () => {
  const [loadingAction, setLoadingAction] = useState(false);
  const [form] = Form.useForm();
  const { t } = useTranslation();
  var router = useRouter();
  const inputRef = useRef(null);

  useEffect(() => {
    setTimeout(() => {
      inputRef.current?.focus();
    }, 10);
  }, []);

  const onOk = () => {
    form
      .validateFields()
      .then(async (values) => {
        setLoadingAction(true);
        var result = await axioshelper(
          MethodType.POST,
          BaseUrlType.Public_API,
          `user/reset-password`,
          {
            email: values.email,
            password: values.password,
            confirmPassword: values.confirmPassword,
            code: values.code,
          }
        );
        setLoadingAction(false);
        if (result?.status == "SUCCESS") {
          showSweetAlert(
            t("site:success"),
            t("site:successMessagev2"),
            "success"
          ).then(() => {
            router.push("/login");
          });
        } else {
          toast.error(result.data.message);
        }
      })
      .catch(() => {});
  };

  return (
    <UserEnter>
      <h1>{t("site:welcome")}</h1>
      <p className="right-body-welcome">{t("site:resetPasswordEVisa")}</p>
      <Form form={form} layout="vertical">
        <Form.Item
          className="form-group"
          label={t("site:email")}
          name="email"
          rules={[
            {
              required: true,
              type: "email",
            },
          ]}
        >
          <Input
            ref={inputRef}
            className="form-control"
            onPressEnter={() =>
              form
                .validateFields()
                .then(() => {
                  onOk();
                })
                .catch(() => {})
            }
          />
        </Form.Item>
        <Form.Item
          className="form-group"
          label={t("site:code")}
          name="code"
          rules={[
            {
              required: true,
            },
          ]}
        >
          <Input
            ref={inputRef}
            className="form-control"
            onPressEnter={() =>
              form
                .validateFields()
                .then(() => {
                  onOk();
                })
                .catch(() => {})
            }
          />
        </Form.Item>
        <Form.Item
          className="form-group"
          label={t("form:password")}
          name="password"
          rules={[
            {
              required: true,
            },
          ]}
        >
          <Input.Password
            className="form-control-password"
            onPressEnter={() =>
              form
                .validateFields()
                .then(() => {
                  onOk();
                })
                .catch(() => {})
            }
          />
        </Form.Item>
        <Form.Item
          className="form-group"
          label={t("form:passwordConfirm")}
          name="confirmPassword"
          rules={[
            {
              required: true,
            },
          ]}
        >
          <Input.Password
            className="form-control-password"
            onPressEnter={() =>
              form
                .validateFields()
                .then(() => {
                  onOk();
                })
                .catch(() => {})
            }
          />
        </Form.Item>
        <Button
          type="primary"
          className="btn-submit"
          onClick={() => onOk()}
          loading={loadingAction}
        >
          {t("form:send")}
        </Button>
      </Form>
    </UserEnter>
  );
};

export default ResetPassword;
