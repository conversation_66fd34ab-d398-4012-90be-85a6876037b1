{"private": true, "scripts": {"dev": "@powershell copy '.env.development.sample' '.env' && next dev -p 4001", "build": "@powershell copy '.env.production.sample' '.env' && next build && copyfiles .next/static/** .next/standalone/ && copyfiles public/** .next/standalone/ && copyfiles nodeService.js .next/standalone/ && copyfiles run.js .next/standalone/ ", "build-test": "@powershell copy '.env.test.sample' '.env' && next build && copyfiles .next/static/** .next/standalone/ && copyfiles public/** .next/standalone/ && copyfiles nodeService.js .next/standalone/ && copyfiles run.js .next/standalone/ ", "start": "next start -p 8099", "export": "next export", "starttest": "set PORT=8095 && node server.js"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@next/font": "13.1.6", "@reduxjs/toolkit": "^1.9.3", "@types/react": "^17.0.38", "@typescript-eslint/eslint-plugin": "^5.8.0", "@typescript-eslint/parser": "^5.8.0", "animate.css": "^4.1.1", "antd": "^5.1.7", "antd-img-crop": "^4.12.2", "axios": "^1.3.1", "bootstrap": "^5.3.0", "child_process": "^1.0.2", "cookies-next": "^2.1.1", "copy-webpack-plugin": "^11.0.0", "cors": "^2.8.5", "eslint": "8.33.0", "eslint-config-next": "13.1.6", "form-data": "^4.0.0", "idempotent-babel-polyfill": "^7.4.4", "moment": "^2.29.4", "next": "^13.1.6", "next-auth": "^4.22.1", "next-redux-wrapper": "^8.1.0", "next-translate": "^1.6.0", "node-windows": "^1.0.0-beta.8", "oidc-client": "^1.11.5", "react": "18.2.0", "react-bootstrap": "^2.7.4", "react-dom": "18.2.0", "react-google-recaptcha": "^3.1.0", "react-icons": "^4.9.0", "react-input-mask": "^3.0.0-alpha.2", "react-moment": "^1.1.3", "react-redux": "^8.0.5", "react-toastify": "^9.1.1", "react-usestateref": "^1.0.8", "redux": "^4.2.1", "redux-devtools-extension": "^2.13.9", "redux-thunk": "^2.4.2", "sweetalert2": "^11.4.8", "sweetalert2-react-content": "^5.0.7", "typescript-eslint": "*", "uuid": "^9.0.0", "sharp": "^0.32.6"}, "devDependencies": {"@types/node": "18.13.0", "@types/react": "18.0.28", "copyfiles": "^2.4.1", "typescript": "4.9.5"}}