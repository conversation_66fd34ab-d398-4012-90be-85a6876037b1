# File reference: env.production.sample

NEXT_PUBLIC_API_URL=https://staging.gateway.com.tr:8010/api
NEXTAUTH_URL=https://evisa.gatewayinternational.com.tr
NEXT_PUBLIC_CMS_URL=http://127.0.0.1:1337/api
NEXTAUTH_SECRET = scRtKy2023GtWy

IdentityServer4_Issuer=http://10.31.40.37:8000
IdentityServer4_CLIENT_ID=gw-evisa-public
IdentityServer4_CLIENT_SECRET="23aa0192-139b-5306-cd13-ff93b45e361c"
NODE_TLS_REJECT_UNAUTHORIZED=0
IDENTITY_SCOPES = "openid offline_access email roles profile userid IdentityServerApi"

NEXT_PUBLIC_RECAPTCHA_SITE_KEY = "6LcP8JEnAAAAAGLV1cG9YFR4NxVn1fCth2sm9B8c"
RECAPTCHA_PRIVATE_KEY = "6LcP8JEnAAAAAJ2LmwFsILSwYBWTHoXr9KpIZHGR"