import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Badge,
  Spin,
  Alert,
} from "antd";
import useTranslation from "next-translate/useTranslation";
import { useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import axioshelper, {
  BaseUrlType,
  MethodType,
} from "../../pages/api/axioshelper";
import { InfoCircleOutlined, SearchOutlined } from "@ant-design/icons";
import { setStep } from "../../store/reducer/stepperSlice";
import { toast } from "react-toastify";
import useStateRef from "react-usestateref";
import dayjs from "dayjs";
import {
  setInvestigation,
  setInvestigationMember,
  setCountry,
  clearApplication,
  setPrecondition,
} from "../../store/reducer/applicaitonSlice";
import { showHideLoader } from "../../store/reducer/loaderSlice";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";
import {
  BsCalendarDate,
  BsCalendarMonth,
  BsCardChecklist,
  BsFillCreditCardFill,
  BsFillFileEarmarkFill,
  BsFillFilePersonFill,
  BsFillPersonVcardFill,
} from "react-icons/bs";
import { BiShow } from "react-icons/bi";
import { GiPassport } from "react-icons/gi";
import { FaGlobeEurope, FaHome, FaRegEdit, FaUserPlus } from "react-icons/fa";
import { MdFamilyRestroom } from "react-icons/md";
import utc from "dayjs/plugin/utc";
import { LoadingOutlined } from "@ant-design/icons";
import { getDateFormat } from "../../utils/tools";

const MyApplications = () => {
  React.useLayoutEffect = React.useEffect;
  const dispatch = useDispatch();
  const { data: session, status } = useSession();
  var router = useRouter();
  var [endDate, setEndDate] = useState(dayjs(new Date()));
  var [startDate, setStartDate] = useState(
    dayjs(new Date().setDate(new Date().getDate() - 7))
  );
  const [preconditions, setPreconditions] = useState([]);
  const [isImage, setIsImage] = useState(true);
  const [detailOpen, setDetailOpen] = useState(false);
  const [fileOpen, setFileOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState();
  const [selectedInvestigation, setSelectedInvestigation] = useState();
  const [investigations, setInvestigations, investigationsRef] = useStateRef(
    []
  );
  const [selectedPrecondition, setSelectedPrecondition] = useState();
  const [loading, setLoading] = useState(false);
  var [investigationFiles, setInvestigationFiles] = useState([]);
  var [previewImage, setPreviewImage] = useState("");
  const [previewTitle, setPreviewTitle] = useState("");
  const handleCancel = () => setDetailOpen(false);
  const handleCancelFile = () => setFileOpen(false);
  dayjs.extend(utc);
  const { t } = useTranslation();

  useEffect(() => {
    if (status == "authenticated") {
      Search();
      dispatch(clearApplication());
      dispatch(setStep(0));
    }

    return () => {
      dispatch(showHideLoader(false));
    };
  }, [status, router.locale]);

  const displayDetail = async (member) => {
    var investigation = investigationsRef.current.find((e) =>
      e.investigationMembers.find((i) => i.id == member.id)
    );

    setSelectedInvestigation(investigation);
    setInvestigationFiles(member?.memberFiles);
    setSelectedMember(member);
    setDetailOpen(true);
  };

  const getApplicationPdf = async () => {
    setLoading(true);
    const result = await axioshelper(
      MethodType.GET,
      BaseUrlType.Public_API,
      `investigations/${selectedMember?.investigationId}/members/${selectedMember?.id}/applicationPdf`,
      null
    );

    if (result?.status == "SUCCESS") {
      if (result.data?.fileContent != null) {
        var fileContent = "";
        fileContent = `data:application/pdf;base64, ${result.data?.fileContent}`;
        setIsImage(false);
        setPreviewImage(fileContent);
        setPreviewTitle(t("site:evisaPDF"));
        setFileOpen(true);
      }
    }
    setLoading(false);
  };

  function getStatusColor(id) {
    var color = "";
    if (id == 1) color = "#5bc0de";
    else if (id == 2) color = "#5cb85c";
    else if (id == 3) color = "#f0ad4e";
    else if (id == 4) color = "#eb4034";
    return color;
  }

  const getPreconditions = async (countryId, passportTypeId, visaTypeId) => {
    const result = await axioshelper(
      MethodType.GET,
      BaseUrlType.Public_API,
      `preconditions/${countryId}`,
      null
    );

    if (result?.status == "SUCCESS") {
      setPreconditions(result?.data.preconditions);
      var precondition = result?.data?.preconditions.find(
        (f) => f.passportTypeId == passportTypeId && f.countryId == countryId
      );

      setSelectedPrecondition(precondition);

      var precoditionData = {
        countryId: countryId,
        passportTypeId: passportTypeId,
        visaTypeId: visaTypeId,
        precondition: precondition,
      };

      dispatch(setPrecondition(precoditionData));
    }
  };

  const movePaymentStep = async (investigation) => {
    dispatch(clearApplication());

    var dispatchInvestigation = {
      id: investigation.id,
      status: investigation.status,
    };
    dispatch(setInvestigation(dispatchInvestigation));
    dispatch(setStep(4));
    router.push("/application/new-application");
  };

  const updateInvestigation = async (member) => {
    dispatch(clearApplication());
    var investigation = investigationsRef.current.find((e) =>
      e.investigationMembers.find((i) => i.id == member.id)
    );

    var dispatchInvestigation = {
      id: investigation.id,
      currentMemberId: member.id,
      status: investigation.status,
      investigationTypeId: investigation.investigationTypeId,
    };

    dispatch(setInvestigation(dispatchInvestigation));

    if (member?.nationality != null) {
      dispatch(setCountry(member.nationality.id));
      getPreconditions(
        member.nationality.id,
        member.passportType?.id,
        member.visaType?.id
      );
    }

    var dispatchMember = {
      investigationMemberId: member.id,
      name: member.name,
      surname: member.surname,
      nationalityId: member.nationality?.id,
      passportTypeId: member.passportType?.id,
      visaTypeId: member.visaType?.id,
      birthdate: dayjs(member.birthDate).format("DD/MM/YYYY"),
      visaEntryDate: dayjs(member.visaEntryDate).format("DD/MM/YYYY"),
      visaExitDate: dayjs(member.visaExitDate).format("DD/MM/YYYY"),
      fatherName: member.fatherName,
      motherName: member.motherName,
      files: [],
      investigationTypeId: investigation.investigationTypeId,
      passportNumber: member.passportNumber,
      passportIssueDate: dayjs(member.passportIssueDate).format("DD/MM/YYYY"),
      passportExpireDate: dayjs(member.passportExpireDate).format("DD/MM/YYYY"),
      memberStatus: member.memberStatus,
    };

    if (member?.memberFiles?.length > 0) {
      var files = member?.memberFiles.map((f) => {
        return {
          id: f?.fileType.id,
          explanation: f?.fileType.displayValue,
          state: 2, //başarılı yüklenmiş,
          file: null,
          fileId: f?.id,
        };
      });
      dispatchMember.files = files;
    }

    dispatch(setInvestigationMember(dispatchMember));
    var otherMembers = investigation?.investigationMembers.filter(
      (i) => i.id != member.id
    );

    if (otherMembers.length > 0) {
      otherMembers.forEach((member) => {
        var dispatchMember = {
          investigationMemberId: member.id,
          name: member.name,
          surname: member.surname,
          nationalityId: member.nationality?.id,
          passportTypeId: member.passportType?.id,
          visaTypeId: member.visaType?.id,
          birthdate: dayjs(member.birthDate).format("DD/MM/YYYY"),
          visaEntryDate: dayjs(member.visaEntryDate).format("DD/MM/YYYY"),
          visaExitDate: dayjs(member.visaExitDate).format("DD/MM/YYYY"),
          fatherName: member.fatherName,
          motherName: member.motherName,
          files: [],
          investigationTypeId: investigation.investigationTypeId,
          passportNumber: member.passportNumber,
          passportIssueDate: dayjs(member.passportIssueDate).format(
            "DD/MM/YYYY"
          ),
          passportExpireDate: dayjs(member.passportExpireDate).format(
            "DD/MM/YYYY"
          ),
          memberStatus: member.memberStatus,
        };
        dispatch(setInvestigationMember(dispatchMember));
      });
    }

    if (member?.memberStatus.id == 3) dispatch(setStep(2)); //revizyona geldiyse
    else dispatch(setStep(1));

    router.push("/application/new-application/");
  };

  const onFileChanged = async (id) => {
    var selectedFile = investigationFiles.find((f) => f.id == id);
    if (selectedFile != null && selectedFile != undefined) {
      var result = await axioshelper(
        MethodType.GET,
        BaseUrlType.Public_API,
        `investigations/${selectedInvestigation?.id}/members/${selectedMember?.id}/files/${selectedFile.id}`,
        null
      );

      if (result?.status == "SUCCESS") {
        var fileName = result.data?.fileName;
        const extension = fileName.split(".").pop().toLowerCase();

        if (result.data?.fileContent != null) {
          var fileContent = "";
          if (extension == "png" || extension == "jpeg" || extension == "jpg") {
            fileContent = `data:image/${extension};base64, ${result.data?.fileContent}`;
            setIsImage(true);
          } else if (extension == "pdf") {
            fileContent = `data:application/pdf;base64, ${result.data?.fileContent}`;
            setIsImage(false);
          } else {
            toast.warning(t("site:unsupportedExtension"));
            return;
          }

          setPreviewImage(fileContent);
          setPreviewTitle(selectedFile?.fileType.displayValue);
          setFileOpen(true);
        }
      }
    }
  };

  const Search = async () => {
    var formattedStart = dayjs.utc(
      dayjs(startDate).format("DD/MM/YYYY 00:00:00"),
      "DD/MM/YYYY HH:mm:ss"
    );
    var formattedEnd = dayjs.utc(
      dayjs(endDate).format("DD/MM/YYYY 23:59:59"),
      "DD/MM/YYYY HH:mm:ss"
    );
    if (formattedStart > formattedEnd) {
      toast.warning(t("form:dateCheckLater"));
    } else {
      var model = {};
      model.startedAt = formattedStart;
      model.endAt = formattedEnd;
      dispatch(showHideLoader(true));
      var result = await axioshelper(
        MethodType.POST,
        BaseUrlType.Public_API,
        `investigations/my-investigations`,
        model
      );
      dispatch(showHideLoader(false));
      if (result?.status == "SUCCESS") {
        setInvestigations(result.data);
      } else {
        setInvestigations([]);
      }
    }
  };

  const addNewMember = async (investigation) => {
    dispatch(clearApplication());

    var dispatchInvestigation = {
      id: investigation.id,
      currentMemberId: 0,
      status: investigation.status,
      investigationTypeId: investigation.investigationTypeId,
    };

    var visaEntryDate = investigation?.investigationMembers[0]?.visaEntryDate;

    var model = {
      investigationTypeId: investigation.investigationTypeId,
      visaEntryDate: dayjs(visaEntryDate).format("DD/MM/YYYY"),
    };

    if (investigation?.investigationMembers?.length > 0) {
      investigation?.investigationMembers?.forEach((member) => {
        var dispatchMember = {
          investigationMemberId: member.id,
          name: member.name,
          surname: member.surname,
          nationalityId: member.nationality?.id,
          passportTypeId: member.passportType?.id,
          visaTypeId: member.visaType?.id,
          birthdate: dayjs(member.birthDate).format("DD/MM/YYYY"),
          visaEntryDate: dayjs(member.visaEntryDate).format("DD/MM/YYYY"),
          visaExitDate: dayjs(member.visaExitDate).format("DD/MM/YYYY"),
          fatherName: member.fatherName,
          motherName: member.motherName,
          files: [],
          investigationTypeId: investigation.investigationTypeId,
          passportNumber: member.passportNumber,
          passportIssueDate: dayjs(member.passportIssueDate).format(
            "DD/MM/YYYY"
          ),
          passportExpireDate: dayjs(member.passportExpireDate).format(
            "DD/MM/YYYY"
          ),
          memberStatus: member.memberStatus,
        };
        dispatch(setInvestigationMember(dispatchMember));
      });
    }

    dispatch(setInvestigation(dispatchInvestigation));
    dispatch(setStep(0));
    setTimeout(() => {
      router.push(
        { path: "/application/new-application", query: model },
        "/application/new-application"
      );
    }, 500);
  };

  function addNewMemberButon(investigation) {
    var button = null;
    if (
      investigation?.investigationTypeId == 2 &&
      investigation.investigationMembers.length < 10
    ) {
      //aile
      button = (
        <Tooltip
          placement="top"
          title={t("site:addNewMember")}
          color={"purple"}
        >
          <Button
            onClick={() => {
              addNewMember(investigation);
            }}
            className="bg-transparent border-0"
          >
            <FaUserPlus size={24} className="text-purple"></FaUserPlus>
          </Button>
        </Tooltip>
      );
    }
    if (
      investigation?.investigationTypeId == 3 &&
      investigation.investigationMembers.length < 300
    ) {
      //grup
      button = (
        <Tooltip
          placement="top"
          title={t("site:addNewMember")}
          color={"purple"}
        >
          <Button
            onClick={() => {
              addNewMember(investigation);
            }}
            className="bg-transparent border-0"
          >
            <FaUserPlus size={24} className="text-purple"></FaUserPlus>
          </Button>
        </Tooltip>
      );
    }
    return button;
  }

  function addTypeBadge(investigation) {
    var badge = null;
    if (investigation?.investigationTypeId == 1) {
      //bireysel
      badge = (
        <Tooltip
          placement="top"
          title={t("site:applyIndividual")}
          color={"green"}
        >
          <Button className="bg-transparent border-0">
            <Badge color="green" />
          </Button>
        </Tooltip>
      );
    } else if (investigation?.investigationTypeId == 2) {
      //aile
      badge = (
        <Tooltip placement="top" title={t("site:applyFamily")} color={"blue"}>
          <Button className="bg-transparent border-0">
            <Badge color="blue" />
          </Button>
        </Tooltip>
      );
    } else if (investigation?.investigationTypeId == 3) {
      //grup
      badge = (
        <Tooltip placement="top" title={t("site:applyGroup")} color={"volcano"}>
          <Button className="bg-transparent border-0">
            <Badge color="volcano" />
          </Button>
        </Tooltip>
      );
    }

    return badge;
  }

  function addCompletePaymentButon(investigation) {
    var button = null;
    if (investigation?.status?.id == 2 || investigation?.status?.id == 4) {
      //ödeme tamamlandı, iptal edildi
      return button;
    }

    if (investigation?.status?.id == 3) {
      button = (
        <Tooltip
          placement="top"
          title={t("site:completePayment")}
          color={"purple"}
        >
          <Button
            onClick={() => {
              movePaymentStep(investigation);
            }}
            className="bg-transparent border-0"
          >
            <BsFillCreditCardFill
              size={24}
              className="text-purple"
            ></BsFillCreditCardFill>
          </Button>
        </Tooltip>
      );
    } else {
      var isExistContinue = investigation.investigationMembers.filter(
        (f) => f.memberStatus?.id == 1
      );
      if (isExistContinue?.length == 0) {
        if (
          investigation?.investigationTypeId == 2 &&
          investigation.investigationMembers.length >= 2
        ) {
          //grup
          button = (
            <Tooltip
              placement="top"
              title={t("site:completePayment")}
              color={"purple"}
            >
              <Button
                onClick={() => {
                  movePaymentStep(investigation);
                }}
                className="bg-transparent border-0"
              >
                <BsFillCreditCardFill
                  size={24}
                  className="text-purple"
                ></BsFillCreditCardFill>
              </Button>
            </Tooltip>
          );
        } else if (
          investigation?.investigationTypeId == 3 &&
          investigation.investigationMembers.length >= 10
        ) {
          //grup
          button = (
            <Tooltip
              placement="top"
              title={t("site:completePayment")}
              color={"purple"}
            >
              <Button
                onClick={() => {
                  movePaymentStep(investigation);
                }}
                className="bg-transparent border-0"
              >
                <BsFillCreditCardFill
                  size={24}
                  className="text-purple"
                ></BsFillCreditCardFill>
              </Button>
            </Tooltip>
          );
        }
      }
    }
    return button;
  }

  const columnsDetail = [
    {
      title: t("form:submissionDate"),
      key: "id",
      render: (_, record) =>
        dayjs(record?.submissionDate).format(getDateFormat[router?.locale]),
      width: 160,
    },
    {
      title: t("form:nationality"),
      key: "id",
      render: (_, record) => <label>{record.nationality?.name}</label>,
      width: 160,
    },
    {
      title: t("form:nameSurname"),
      key: "id",
      render: (_, record) => (
        <>
          {record.name} {record.surname}
        </>
      ),
      width: 250,
    },
    {
      title: t("form:birthdate"),
      key: "id",
      render: (_, record) =>
        dayjs(record?.birthDate).format(getDateFormat[router?.locale]),
      width: 150,
    },
    {
      title: t("form:visaStatus"),
      key: "id",
      render: (_, record) =>
        record.visaStatus?.id == 2 ? (
          <label style={{ color: "red", textDecoration: "underline" }}>
            {record?.visaStatus == null
              ? t("site:investigating")
              : record?.visaStatus.displayValue}{" "}
            <InfoCircleOutlined style={{ color: "red" }} />
          </label>
        ) : record.visaStatus?.id == 3 ? (
          <label className="text-warning">
            <Tooltip
              color={"#4C1D95"}
              title={
                <div>
                  <ul>
                    {record.memberMessages.map((m) => (
                      <li key={m.id}>*{m.description}</li>
                    ))}
                  </ul>
                </div>
              }
            >
              {record?.visaStatus == null
                ? t("site:investigating")
                : record?.visaStatus.displayValue}{" "}
              <InfoCircleOutlined style={{ color: "#ffc107" }} />
            </Tooltip>
          </label>
        ) : (
          <label
            style={{ color: record.visaStatus?.id == 1 ? "green" : "blue" }}
          >
            {record?.memberStatus?.id == 1
              ? "-"
              : record?.visaStatus == null
              ? t("site:investigating")
              : record?.visaStatus?.displayValue}
          </label>
        ),
      width: 160,
    },
    {
      title: t("form:applicationStatus"),
      key: "id",
      render: (_, record) => (
        <label>{record?.memberStatus?.displayValue}</label>
      ),
      width: 175,
    },
    {
      title: t("site:actions"),
      key: "action",
      align: "right",
      render: (_, record) => (
        <div>
          {(record.visaStatus == null ||
            record.visaStatus?.id == 4 ||
            record.visaStatus?.id == 3) &&
          (record.memberStatus?.id == 1 || record.memberStatus?.id == 3) ? (
            <Tooltip placement="top" title={t("site:update")} color={"purple"}>
              <Button
                size="small"
                className="bg-transparent border-0"
                onClick={() => {
                  updateInvestigation(record);
                }}
              >
                <FaRegEdit size={22} className="text-purple"></FaRegEdit>
              </Button>
            </Tooltip>
          ) : (
            <></>
          )}
          <Tooltip placement="top" title={t("site:detail")} color={"purple"}>
            <Button
              size="small"
              className="bg-transparent border-0"
              onClick={() => {
                displayDetail(record);
              }}
            >
              <BsCardChecklist
                size={22}
                className="text-purple"
              ></BsCardChecklist>
            </Button>
          </Tooltip>
        </div>
      ),
      width: 175,
    },
  ];

  const loadingIcon = (
    <LoadingOutlined
      style={{
        fontSize: 24,
      }}
    />
  );

  return (
    <>
      <Modal
        className={isImage ? "" : "w-50"}
        open={fileOpen}
        title={previewTitle}
        footer={null}
        onCancel={handleCancelFile}
      >
        {isImage ? (
          <img
            alt=""
            style={{
              width: "100%",
            }}
            src={previewImage}
          />
        ) : (
          <embed
            className="mt-3"
            style={{ width: "100%", minHeight: 450 }}
            src={previewImage}
          ></embed>
        )}
      </Modal>

      <Modal
        className="w-75"
        open={detailOpen}
        centered
        footer={null}
        onCancel={handleCancel}
      >
        {selectedMember?.memberMessages.length > 0 ? (
          <div
            className="alert alert-primary d-flex align-items-center mt-3 mb-0 pb-0"
            role="alert"
          >
            <ul>
              {selectedMember?.memberMessages.map((m) => (
                <li key={m.id}>*{m.description}</li>
              ))}
            </ul>
          </div>
        ) : (
          <></>
        )}
        <Spin indicator={loadingIcon} spinning={loading}>
          {selectedMember?.visaStatus?.id == 1 ? (
            <div className="alert alert-success" role="alert">
              {t("site:applicationPdfWarning1")} {"  "}
              <a onClick={getApplicationPdf} className="alert-link">
                {t("site:previewPdf")}
                {"  "}
              </a>
              {t("site:applicationPdfWarning2")}
            </div>
          ) : (
            <></>
          )}
        </Spin>

        <Badge.Ribbon
          text={selectedMember?.visaStatus?.displayValue}
          color={
            selectedMember?.visaStatus?.id == 1
              ? "green"
              : selectedMember?.visaStatus?.id == 2
              ? "red"
              : "blue"
          }
          className="p-1"
        >
          <div className="card mt-4">
            <div className="card-body">
              <div className="row">
                <div className="col-12">
                  <h6 className="text-purple fw-bold">
                    {t("form:passportInfo")}
                  </h6>
                  <div className="row">
                    <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                      <table>
                        <tbody>
                          <tr>
                            <td>
                              <GiPassport size={30} className="icon-purple" />{" "}
                            </td>
                            <td>
                              <label
                                className="text-muted preview-title fw-bold"
                                style={{ display: "block" }}
                              >
                                {t("form:passportType")}
                              </label>
                              <label className="preview-subtitle">
                                {selectedMember?.passportType?.displayValue}
                              </label>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                      <table>
                        <tbody>
                          <tr>
                            <td>
                              <BsFillPersonVcardFill
                                size={30}
                                className="icon-purple me-2"
                              />{" "}
                            </td>
                            <td>
                              <label
                                className="text-muted fw-bold preview-title"
                                style={{ display: "block" }}
                              >
                                {t("form:visaType")}
                              </label>
                              <label className="preview-subtitle">
                                {selectedMember?.visaType?.displayValue}
                              </label>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                      <table>
                        <tbody>
                          <tr>
                            <td>
                              <BsCalendarDate
                                size={30}
                                className="icon-purple me-2"
                              />{" "}
                            </td>
                            <td>
                              <label
                                className="text-muted fw-bold preview-title"
                                style={{ display: "block" }}
                              >
                                {t("form:entryExitDate")}
                              </label>
                              <label className="preview-subtitle">
                                {dayjs(selectedMember?.visaEntryDate).format(
                                  getDateFormat[router?.locale]
                                )}
                                {" - "}
                                {dayjs(selectedMember?.visaExitDate).format(
                                  getDateFormat[router?.locale]
                                )}
                              </label>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div className="row">
                    <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                      <table>
                        <tbody>
                          <tr>
                            <td>
                              <GiPassport size={30} className="icon-purple" />{" "}
                            </td>
                            <td>
                              <label
                                className="text-muted preview-title fw-bold"
                                style={{ display: "block" }}
                              >
                                {t("form:passport")}
                              </label>
                              <label className="preview-subtitle">
                                {selectedMember?.passportNumber}
                              </label>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                      <table>
                        <tbody>
                          <tr>
                            <td>
                              <BsCalendarDate
                                size={30}
                                className="icon-purple me-2"
                              />{" "}
                            </td>
                            <td>
                              <label
                                className="text-muted fw-bold preview-title"
                                style={{ display: "block" }}
                              >
                                {t("form:passportIssueDate")}
                              </label>
                              <label className="preview-subtitle">
                                {dayjs(
                                  selectedMember?.passportIssueDate
                                ).format(getDateFormat[router?.locale])}
                              </label>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                      <table>
                        <tbody>
                          <tr>
                            <td>
                              <BsCalendarDate
                                size={30}
                                className="icon-purple me-2"
                              />{" "}
                            </td>
                            <td>
                              <label
                                className="text-muted fw-bold preview-title"
                                style={{ display: "block" }}
                              >
                                {t("form:passportExpireDate")}
                              </label>
                              <label className="preview-subtitle">
                                {dayjs(
                                  selectedMember?.passportExpireDate
                                ).format(getDateFormat[router?.locale])}
                              </label>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
                <div className="col-12">
                  <Divider></Divider>
                  <h6 className="text-purple fw-bold">
                    {t("form:personalInfo")}
                  </h6>
                  <div className="row">
                    <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                      <div className="d-flex flex-row align-items-center">
                        <div>
                          <BsFillFilePersonFill
                            size={30}
                            className="icon-purple"
                          />
                        </div>
                        <div className="d-flex flex-column ms-2 slf-preview-width">
                          <label className="text-muted fw-bold preview-title">
                            {t("form:nameSurname")}
                          </label>
                          <span className="preview-subtitle">
                            {selectedMember?.name} {selectedMember?.surname}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                      <div className="d-flex flex-row align-items-center">
                        <div>
                          <FaGlobeEurope size={30} className="icon-purple" />
                        </div>
                        <div className="d-flex flex-column ms-2 slf-preview-width">
                          <label className="text-muted fw-bold preview-title">
                            {t("form:nationality")}
                          </label>
                          <span className="preview-subtitle">
                            {selectedMember?.nationality.name}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                      <table>
                        <tbody>
                          <tr>
                            <td>
                              <BsCalendarMonth
                                size={30}
                                className="icon-purple me-2"
                              />{" "}
                            </td>
                            <td>
                              <label
                                className="text-muted fw-bold preview-title"
                                style={{ display: "block" }}
                              >
                                {t("form:birthdate")}
                              </label>
                              <label className="preview-subtitle">
                                {dayjs(selectedMember?.birthDate).format(
                                  getDateFormat[router?.locale]
                                )}
                              </label>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                      <div className="d-flex flex-row align-items-center">
                        <div>
                          <MdFamilyRestroom size={30} className="icon-purple" />
                        </div>
                        <div className="d-flex flex-column ms-2 slf-preview-width">
                          <label className="text-muted fw-bold preview-title">
                            {t("form:fatherMotherName")}
                          </label>
                          <span className="preview-subtitle">
                            {selectedMember?.fatherName} -{" "}
                            {selectedMember?.motherName}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-12">
                  <Divider></Divider>
                  <h6 className="text-purple fw-bold">{t("form:files")}</h6>
                  <div className="row">
                    {selectedMember?.memberFiles?.map((item) => (
                      <div
                        className="col-lg-6 col-md-6 col-sm-12 mt-4"
                        key={item.id}
                      >
                        <div className="card">
                          <div className="card-body">
                            <div className="d-flex align-items-center">
                              <div className="preview-subtitle">
                                <BsFillFileEarmarkFill
                                  size={30}
                                  className="icon-purple me-2"
                                />{" "}
                                {item.fileType.displayValue}
                              </div>
                              <div className="ms-auto">
                                <Button
                                  type="text"
                                  onClick={() => onFileChanged(item.id)}
                                >
                                  {" "}
                                  <BiShow className="icon-purple me-2" />{" "}
                                  {t("site:show")}
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Badge.Ribbon>
      </Modal>

      <div className="container slf-container mb-5 pt-3">
        <div className="slf-searchbar-flex-wrap">
          <div className="slf-searchbar-flex">
            <DatePicker
              format={getDateFormat[router?.locale]}
              style={{ width: "100%" }}
              defaultValue={startDate}
              disabledDate={(current) => {
                return current && current > new Date();
              }}
              onChange={(date) => {
                var formattedStart = dayjs.utc(
                  dayjs(date).format("DD/MM/YYYY 00:00:00"),
                  "DD/MM/YYYY HH:mm:ss"
                );
                setStartDate(formattedStart);
              }}
            />
          </div>
          <div className="slf-searchbar-flex">
            <DatePicker
              format={getDateFormat[router?.locale]}
              style={{ width: "100%" }}
              defaultValue={endDate}
              disabledDate={(current) => {
                return current && current > new Date();
              }}
              onChange={(date) => {
                var formattedEnd = dayjs.utc(
                  dayjs(date).format("DD/MM/YYYY 23:59:59"),
                  "DD/MM/YYYY HH:mm:ss"
                );
                setEndDate(formattedEnd);
              }}
            />
          </div>
          <div className="slf-searchbar-flex">
            <Button
              block
              size="medium"
              icon={<SearchOutlined />}
              onClick={() => {
                Search();
              }}
              className="bg-purple"
            >
              {t("form:search")}
            </Button>
          </div>
        </div>
        {investigations?.length == 0 ? (
          <Alert
            message={t("site:info")}
            description={t("site:noWaitingRecords")}
            type="info"
            showIcon
            className="mt-4"
          />
        ) : (
          investigations?.map((investigation) => (
            <div className="row mt-3" key={investigation.id}>
              <div className="col">
                <Badge.Ribbon
                  text={investigation?.status?.displayValue}
                  color={getStatusColor(investigation?.status?.id)}
                  className="p-1"
                >
                  <div className="card">
                    <div className="card-header">
                      <div>
                        {addTypeBadge(investigation)}
                        {addCompletePaymentButon(investigation)}
                        {investigation?.status?.id == 1 &&
                        investigation?.investigationTypeId != 1
                          ? addNewMemberButon(investigation)
                          : null}
                      </div>
                    </div>
                    <div className="card-body">
                      {investigation?.investigationMembers?.length > 0 ? (
                        <div className="table-responsive">
                          <Table
                            className="table"
                            tableLayout="fixed"
                            columns={columnsDetail}
                            dataSource={investigation?.investigationMembers}
                            rowKey={"id"}
                            pagination={false}
                          />
                        </div>
                      ) : (
                        <></>
                      )}
                    </div>
                  </div>
                </Badge.Ribbon>
              </div>
            </div>
          ))
        )}
      </div>
    </>
  );
};

export default MyApplications;
