import { But<PERSON> } from "antd";
import { signOut, useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { useState, useEffect } from "react";
import { Dropdown } from "react-bootstrap";
import Container from "react-bootstrap/Container";
import Nav from "react-bootstrap/Nav";
import Navbar from "react-bootstrap/Navbar";
import NavDropdown from "react-bootstrap/NavDropdown";
import localeTR from "antd/locale/tr_TR";
import localeEN from "antd/locale/en_US";
import useTranslation from "next-translate/useTranslation";
import { UserOutlined } from "@ant-design/icons";
import Link from "next/link";
import { useDispatch } from "react-redux";
import { setStep } from "../store/reducer/stepperSlice";
import { clearApplication } from "../store/reducer/applicaitonSlice";
import {
  IconUser,
  IconQuestionMark,
  IconEntry,
  IconSupport,
  IconFiles,
  IconSocials,
} from "../utils/icons";
import Image from "next/image";
import ScrollButton from "../components/scroll-button";

const Layoutv2 = ({ children }) => {
  const dispatch = useDispatch();
  const [locale, setLocale] = useState(localeEN);
  const route = useRouter();
  const { data: session, status } = useSession();
  const { t } = useTranslation();
  const dateForFooter = new Date().getFullYear();

  useEffect(() => {
    if (
      status == "authenticated" &&
      (route?.pathname?.includes("login") ||
        route?.pathname?.includes("register") ||
        route?.pathname?.includes("forgot-password"))
    ) {
      window.location.href = "/";
    }
  }, [status]);

  function dChange(e) {
    localStorage.setItem("locale", e);
    setLocale(localeTR);
    route.push(
      {
        path: route.asPath,
        query: route.query,
      },
      "",
      { locale: e }
    );
  }

  return (
    <>
      <Navbar
        collapseOnSelect
        expand="lg"
        variant="dark"
        className="nav-purple-gradient"
      >
        <Container className="slf-container">
          <Link href="/" className="slf-logo">
            <span className="slf-t1">E-visa</span>
            <span className="slf-t2">Türkiye</span>
          </Link>
          <Navbar.Toggle aria-controls="responsive-navbar-nav" />

          <Navbar.Collapse id="responsive-navbar-nav">
            {/* OK: Diğer sabit linkler */}
            <Nav className="ms-auto slf-header-links">
              {/* <Nav.Link className="slf-header-link">{t("site:aboutUs")}</Nav.Link>
              <Nav.Link className="slf-header-link">{t("site:applicationSteps")}</Nav.Link>
              <Nav.Link className="slf-header-link">{t("site:sss")}</Nav.Link>
              <Nav.Link className="slf-header-link">{t("site:communication")}</Nav.Link> */}
            </Nav>

            <Nav>
              {status == "loading" ? null : status == "authenticated" &&
                !route?.pathname?.includes("login") &&
                !route?.pathname?.includes("register") &&
                !route?.pathname?.includes("forgot-password") ? (
                <>
                  <Nav className="slf-header-links">
                    <Nav.Link
                      className="slf-header-link"
                      onClick={() => {
                        route.push("/application/navigation-page");
                        setTimeout(() => {
                          dispatch(setStep(0));
                          dispatch(clearApplication());
                        }, 1000);
                      }}
                    >
                      {t("site:newApplication")}
                    </Nav.Link>
                    <Nav.Link
                      className="slf-header-link"
                      onClick={() => {
                        route.push("/application/my-applications");
                      }}
                    >
                      {t("site:myApplications")}
                    </Nav.Link>
                  </Nav>
                  <Dropdown className="slf-profile-menu">
                    <Dropdown.Toggle className="slf-profile-menu-toggle">
                      <UserOutlined />
                      <span>{session?.user?.name}</span>
                    </Dropdown.Toggle>
                    <Dropdown.Menu>
                      <Dropdown.Item className="slf-dropdown-item">
                        <Button
                          type="link"
                          className="slf-dropdown-item-button"
                          onClick={() => {
                            route.push("/profile");
                          }}
                        >
                          <IconUser className={"mr-14px"} />
                          {t("site:profile")}
                        </Button>
                      </Dropdown.Item>
                      <Dropdown.Item className="slf-dropdown-item">
                        <Button
                          type="link"
                          className="slf-dropdown-item-button"
                          onClick={() => {
                            route.push("/feedback");
                          }}
                        >
                          <IconQuestionMark className={"mr-14px"} />
                          {t("site:feedback")}
                        </Button>
                      </Dropdown.Item>
                      {/* <Dropdown.Item className="slf-dropdown-item">
                        <Button
                          type="link"
                          className="slf-dropdown-item-button"
                          onClick={() => {
                            route.push("/call-center");
                          }}
                        >
                          <IconPhone className={"mr-14px"} />
                          {t("site:callCenter")}
                        </Button>
                      </Dropdown.Item> */}
                      {/* <Dropdown.Item className="slf-dropdown-item">
                        <Button
                          type="link"
                          className="slf-dropdown-item-button"
                          onClick={() => {
                            route.push("/agency");
                          }}
                        >
                          <IconTablerFlag className={"mr-14px"} />
                          {t("site:authorizedAgentList")}
                        </Button>
                      </Dropdown.Item> */}
                      <Dropdown.Item className="slf-dropdown-item">
                        <Button
                          type="link"
                          className="slf-dropdown-item-button"
                          onClick={async () => {
                            await signOut();
                          }}
                        >
                          <IconEntry className={"mr-14px"} />
                          {t("site:signOut")}
                        </Button>
                      </Dropdown.Item>
                    </Dropdown.Menu>
                  </Dropdown>
                </>
              ) : (
                <>
                  <Nav.Link
                    className="slf-register-button"
                    onClick={() => {
                      route.push("/register");
                    }}
                  >
                    {t("site:register")}
                  </Nav.Link>
                  <Nav.Link
                    className="slf-login-button"
                    onClick={() => {
                      route.push("/login");
                    }}
                  >
                    {t("site:login")}
                  </Nav.Link>
                </>
              )}
            </Nav>

            {/* OK: Dil seçme menüsü */}
            <Nav>
              <NavDropdown
                align={"end"}
                title={
                  <Image
                    className="slf-lang-switcher-img"
                    src={`/images/flag_${route.locale}.png`}
                    width={24}
                    height={16}
                    alt={`flag_${route.locale}`}
                  ></Image>
                }
                id="collasible-nav-dropdown"
                className="slf-lang-switcher slf-header-link"
              >
                <NavDropdown.Item className="slf-dropdown-item">
                  <Button
                    type="link"
                    className="slf-dropdown-item-button"
                    onClick={() => {
                      dChange("en");
                    }}
                  >
                    <Image
                      src="/images/flag_en.png"
                      width={24}
                      height={16}
                      alt="flag_en"
                    ></Image>
                    <span className="ml-8px">English</span>
                  </Button>
                </NavDropdown.Item>
                <NavDropdown.Item className="slf-dropdown-item">
                  <Button
                    type="link"
                    className="slf-dropdown-item-button"
                    onClick={() => {
                      dChange("tr");
                    }}
                  >
                    <Image
                      src="/images/flag_tr.png"
                      width={24}
                      height={16}
                      alt="flag_tr"
                    ></Image>
                    <span className="ml-8px">Türkçe</span>
                  </Button>
                </NavDropdown.Item>
              </NavDropdown>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>
      {children}
      {/* OK: Footer */}
      <footer className="slf-footer pt-4">
        <Container className="slf-container">
          <div className="slf-footer-flex">
            {/* <div className="slf-footer-flex-columns">
              <h3>{t("site:application")}</h3>
              <Link href="/application/navigation-page">
                {t("site:newApplication")}
              </Link>
              <Link href="/application/my-applications">
                {t("site:myApplications")}
              </Link>
              <Link href="/#mobileApp">{t("site:mobilApp")}</Link>
              <Link href="">{t("site:legalObligations")}</Link>
            </div> */}
            <div className="slf-footer-flex-columns">
              <h3>{t("site:aboutEVisa")}</h3>
              <Link href="/#howGetVisa">
                {t("site:applicationStepsVideos")}
              </Link>
              <Link href="">{t("site:toInform")}</Link>
              <Link href="">{t("site:complianceCheck")}</Link>
              <Link href="/#frequentlyAskedQuestions">
                {t("site:frequentlyAskedQuestions")}
              </Link>
            </div>
            <div className="slf-footer-flex-columns">
              <h3>{t("site:communication")}</h3>
              <Link href="">{t("site:callCenter")}</Link>
              <Link href="">{t("site:helpCenter")}</Link>
            </div>
            <div className="slf-footer-flex-columns">
              <h3>{t("site:usefulInformation")}</h3>
              <Link href="https://www.mfa.gov.tr/" target="_blank">
                {t("site:ministryOfForeignAffairs")}
              </Link>
              <Link href="https://www.icisleri.gov.tr/" target="_blank">
                {t("site:ministryOfInterior")}
              </Link>
              <Link href="https://www.ktb.gov.tr/" target="_blank">
                {t("site:ministryOfTourism")}
              </Link>
            </div>
            <div className="slf-footer-flex-columns">
              <h3>{t("site:legalObligation")}</h3>
              <Link
                target="_blank"
                locale={false}
                href={`/files/privacy-policy_${route.locale}.pdf`}
              >
                {t("site:privacyPolicy")}
              </Link>
              <Link
                target="_blank"
                locale={false}
                href={`/files/consent-text_${route.locale}.pdf`}
                title="Consent Text"
              >
                {t("site:consentText")}
              </Link>
              <Link
                target="_blank"
                locale={false}
                href={`/files/kvkk_${route.locale}.pdf`}
              >
                {t("site:clarificationText")}
              </Link>
              <Link
                target="_blank"
                locale={false}
                href={`/files/cookie-policy_${route.locale}.pdf`}
              >
                {t("site:cookiePolicy")}
              </Link>
              <Link
                target="_blank"
                locale={false}
                href={`/files/disclaimer_${route.locale}.pdf`}
              >
                {t("site:disclaimer")}
              </Link>
            </div>
            <div className="slf-footer-flex-columns slf-w-417px">
              <h3>{t("site:ourAddressWhereYouCanReachUs")}</h3>
              <p>
                Kocatepe Mah. Aydede Cad. Koray Apt No:16/9 Beyoğlu İstanbul
                {/* Dr. Sadık Ahmet Cad. No: 8 Balgat / <b>ANKARA - TÜRKİYE</b> */}
              </p>
              <p>
                <b>{t("site:phone")}:</b>{" "}
                <Link
                  className="slf-footer-flex-columns-link-inherit"
                  href={"tel:+90 (*************"}
                >
                  +90 (*************
                </Link>
              </p>
              {/* <p>
                <b>{t("site:email")}:</b>{" "}
                <Link
                  className="slf-footer-flex-columns-link-inherit"
                  href={"mailto:<EMAIL>"}
                >
                  <EMAIL>
                </Link>
              </p> */}
              <div className="slf-footer-flex-rows">
                <div className="slf-footer-flex-rows-columns">
                  <div className="slf-footer-flex-rows-columns-rows">
                    <Image
                      src="/img/landing/app-store.png"
                      width={126}
                      height={38}
                      alt="app-store"
                    ></Image>
                    <Image
                      src="/img/landing/play-store.png"
                      width={126}
                      height={38}
                      alt="play-store"
                    ></Image>
                  </div>
                  {/* <div className="slf-footer-flex-rows-columns-rows">
                    <IconSupport />
                    <div>
                      <Link
                        className="slf-footer-flex-rows-columns-rows-link"
                        href={"tel:0850 300 00 00"}
                      >
                        0850 300 00 00
                      </Link>
                      <p className="slf-footer-flex-rows-columns-rows-p">
                        {t("site:our724CallCenter")}
                      </p>
                    </div>
                  </div> */}
                </div>
                {/* <div>
                  <Image
                    src="/img/landing/footer-qr.jpg"
                    width={98}
                    height={98}
                    alt="qr"
                  ></Image>
                </div> */}
              </div>
            </div>
          </div>
          {/* <div className="slf-footer-kvkk">
            <div>
              <IconFiles />
              <span>
                <b>{t("site:kvkk")}</b> {t("site:personalDataProtectionLaw")}{" "}
                <Link
                  target="_blank"
                  locale={false}
                  href={`/files/kvkk_${route.locale}.pdf`}
                >
                  {t("site:readText")}
                </Link>
              </span>
            </div>
            <IconSocials />
          </div> */}
          {/* <div className="slf-footer-tc">
            © {dateForFooter} {t("site:republicOfTurkiyeEVisaWebsite")}
          </div> */}
        </Container>
        <ScrollButton />
      </footer>
    </>
  );
};

export default Layoutv2;
