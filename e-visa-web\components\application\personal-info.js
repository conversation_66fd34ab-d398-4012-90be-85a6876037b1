import {
  <PERSON><PERSON>,
  DatePicker,
  Divider,
  Form,
  Input,
  Spin,
  Upload,
  Collapse,
  Modal,
} from "antd";
import useTranslation from "next-translate/useTranslation";
import { useSession } from "next-auth/react";
import { useLayoutEffect, useState } from "react";
import useStateRef from "react-usestateref";
import dayjs from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import axioshelper, {
  BaseUrlType,
  MethodType,
} from "../../pages/api/axioshelper";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { setStep } from "../../store/reducer/stepperSlice";
import {
  setInvestigation,
  setInvestigationMember,
  setMemberFiles,
} from "../../store/reducer/applicaitonSlice";
import { toast } from "react-toastify";
import { useRouter } from "next/router";
import utc from "dayjs/plugin/utc";
import { getDateFormat } from "../../utils/tools";
import {
  LoadingOutlined,
  PlusOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";
import ImgCrop from "antd-img-crop";
import { v4 as uuidv4 } from "uuid";
import showSweetAlert from "../../utils/sweetAlert";

const PersonalInformation = () => {
  const dispatch = useDispatch();
  const [fileTypes, setFileTypes, fileTypesRef] = useStateRef([]);
  var [residenceDuration, setResidenceDuration] = useState(0);
  const [isContinue, setIsContinue] = useState(false);
  const [isDisable, setIsDisable] = useState(true);
  const [loading, setLoading] = useState(false);
  const [currentMemberId, setCurrentMemberId] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const handleCancel = () => setPreviewOpen(false);
  const [canProceed, setCanProceed] = useStateRef(false);
  const [previewTitle, setPreviewTitle] = useState("");
  const [visaExitDate, setVisaExitDate, visaExitDateRef] = useStateRef();
  const [isFormUpdated, setIsFormUpdated, isFormUpdatedRef] =
    useStateRef(false);

  const { status } = useSession();
  const { t } = useTranslation();
  const router = useRouter();
  const [loadingAction, setLoadingAction] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const [referenceKey, setReferenceKey] = useState();

  const [form] = Form.useForm();
  var precondition = useSelector((state) => state.application.preconditionData);
  var countryId = precondition?.countryId;
  var passportTypeId = precondition?.passportTypeId;
  var visaTypeId = precondition?.visaTypeId;

  var investigation = useSelector((state) => state.application.investigation);
  const currentStep = useSelector((state) => state.stepper.value);
  const companyInfo = router.query;
  const visaEntryDate = router.query.visaEntryDate;
  const { Panel } = Collapse;
  dayjs.extend(utc);
  dayjs.extend(isSameOrAfter);

  useLayoutEffect(() => {
    if (status == "authenticated") {
      setIsFormUpdated(false);
      if (investigation?.currentMemberId == 0) {
        if (companyInfo?.investigationTypeId != 3) form.resetFields();
        else initialForm();
      }

      setPassportImage();
    }
    setCurrentMemberId(investigation?.currentMemberId);
  }, [status, investigation?.currentMemberId]);

  useLayoutEffect(() => {
    const fetchData = async () => {
      if (countryId > 0) {
        await setInitialValues();
      }
      if (
        precondition != null &&
        precondition?.precondition?.totalResidenceDuration != null
      ) {
        setResidenceDuration(
          precondition?.precondition?.totalResidenceDuration
        );
      }
    };

    if (status == "authenticated" && currentStep == 1) {
      fetchData();
    }
  }, [
    countryId,
    investigation.currentMemberId,
    precondition,
    status,
    router.locale,
    currentStep,
  ]);

  function getFileMetaSrc(fileName) {
    var uzanti = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
    if (uzanti == ".jpg" || uzanti == ".jpeg" || uzanti == ".png") {
      uzanti = uzanti.replace(".", "");
      return `data:image/${uzanti};base64, `;
    } else if (uzanti == ".pdf") {
      return `data:application/pdf;base64, `;
    } else {
      return "unsupported";
    }
  }

  const setPassportImage = async () => {
    var fTypes = [];
    var ftype = {};
    setFileTypes([]);
    var existingMember = investigation.members.find(
      (e) => e.investigationMemberId == investigation.currentMemberId
    );
    if (existingMember) {
      var existingMemberFile = existingMember?.files.find(
        (e) => e.id == 5 && e?.state == 2
      );

      if (existingMemberFile) {
        var file = existingMemberFile?.file;
        if (existingMemberFile?.file == null) {
          var fileResult = await axioshelper(
            MethodType.GET,
            BaseUrlType.Public_API,
            `investigations/${investigation.id}/members/${investigation.currentMemberId}/files/${existingMemberFile?.fileId}`,
            null
          );
          if (fileResult.status == "SUCCESS") {
            var meta = getFileMetaSrc(fileResult.data.fileName);
            file = {
              uid: 1,
              url: meta + fileResult.data.fileContent,
              name: fileResult.data.fileName,
              status: "done",
            };
          }
        }
        ftype = {
          id: 5,
          explanation: t("site:passport"),
          state: 2, //0-bekliyor 1- selected 2-başarılı yüklendi 3- hata
          file: file,
          fileId: existingMemberFile?.fileId,
        };
      } else {
        ftype = {
          id: 5,
          explanation: t("site:passport"),
          state: 0,
          file: {
            uid: 5,
            status: "removed",
          },
        };
      }

      var files = existingMember?.files.filter((f) => f.id != 5);

      if (files) {
        files.push(ftype);
        dispatch(
          setMemberFiles({
            investigationMemberId: existingMember?.id,
            files: files,
          })
        );
      }

      fTypes.push(ftype);
      setFileTypes(fTypes);
    } else {
      ftype = {
        id: 5,
        explanation: t("site:passport"),
        state: 0,
        file: {
          uid: 5,
          status: "removed",
        },
      };

      fTypes.push(ftype);
      setFileTypes(fTypes);
    }
    var filterWaiting = fileTypesRef.current.filter((f) => f.state != 2); //başarılı olmayanlar
    if (filterWaiting.length == 0) {
      setCanProceed(true);
    }
  };

  const setInitialValues = async () => {
    if (companyInfo?.investigationTypeId == 3 && visaEntryDate != null) {
      var computedVisaExitDate = new Date(dayjs(visaEntryDate, "DD/MM/YYYY"));
      computedVisaExitDate.setDate(
        computedVisaExitDate.getDate() +
          precondition?.precondition?.totalResidenceDuration
      );
      setVisaExitDate(dayjs(computedVisaExitDate).format("DD/MM/YYYY"));
    }

    if (investigation.currentMemberId > 0) {
      var existing = investigation.members.find(
        (e) => e.investigationMemberId == investigation.currentMemberId
      );

      form.setFieldsValue({
        member: {
          name: existing.name,
          surname: existing.surname,
          nationality: countryId,
          passportType: passportTypeId,
          visaType: visaTypeId,
          birthdate: dayjs(existing.birthdate, "DD/MM/YYYY"),
          visaEntryDate:
            companyInfo?.investigationTypeId == 3
              ? dayjs(visaEntryDate, "DD/MM/YYYY")
              : dayjs(existing.visaEntryDate, "DD/MM/YYYY"),
          visaExitDate:
            companyInfo?.investigationTypeId == 3
              ? dayjs(visaExitDateRef?.current, "DD/MM/YYYY")
              : dayjs(existing.visaExitDate, "DD/MM/YYYY"),
          fatherName: existing.fatherName,
          motherName: existing.motherName,
          passportNumber: existing.passportNumber,
          passportIssueDate: dayjs(existing.passportIssueDate, "DD/MM/YYYY"),
          passportExpireDate: dayjs(existing.passportExpireDate, "DD/MM/YYYY"),
        },
      });
    } else {
      form.setFieldsValue({
        member: {
          name: null,
          surname: null,
          passportType: null,
          visaType: null,
          birthdate: null,
          fatherName: null,
          motherName: null,
          visaEntryDate:
            companyInfo?.investigationTypeId == 3
              ? dayjs(visaEntryDate, "DD/MM/YYYY")
              : null,
          visaExitDate:
            companyInfo?.investigationTypeId == 3
              ? dayjs(visaExitDateRef?.current, "DD/MM/YYYY")
              : null,
        },
      });
    }
  };

  const getBase64 = (file) =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div
        style={{
          marginTop: 8,
        }}
      >
        {t("site:upload")}
      </div>
    </div>
  );

  const onFinish = async (values) => {
    setLoadingAction(true);
    setIsContinue(true);
    var investigationId =
      investigation.id == 0 || investigation.id == undefined
        ? null
        : investigation.id;

    var memberModel = {
      name: values.member.name,
      surname: values.member.surname,
      nationalityId: countryId,
      passportTypeId: passportTypeId,
      visaTypeId: visaTypeId,
      birthdate: dayjs.utc(
        dayjs(values.member.birthdate).format("DD/MM/YYYY 00:00:00"),
        "DD/MM/YYYY HH:mm:ss"
      ),
      visaEntryDate: dayjs.utc(
        dayjs(values.member.visaEntryDate).format("DD/MM/YYYY 00:00:00"),
        "DD/MM/YYYY HH:mm:ss"
      ),
      visaExitDate: dayjs.utc(
        dayjs(values.member.visaExitDate).format("DD/MM/YYYY 00:00:00"),
        "DD/MM/YYYY HH:mm:ss"
      ),
      fatherName: values.member.fatherName,
      motherName: values.member.motherName,
      passportNumber: values.member.passportNumber,
      passportIssueDate: dayjs.utc(
        dayjs(values.member.passportIssueDate).format("DD/MM/YYYY 00:00:00"),
        "DD/MM/YYYY HH:mm:ss"
      ),
      passportExpireDate: dayjs.utc(
        dayjs(values.member.passportExpireDate).format("DD/MM/YYYY 00:00:00"),
        "DD/MM/YYYY HH:mm:ss"
      ),
      investigationTypeId:
        investigation?.investigationTypeId != 0 &&
        investigation?.investigationTypeId != undefined
          ? investigation?.investigationTypeId
          : companyInfo?.investigationTypeId,
      files: [],
      referenceKey: referenceKey,
    };

    if (investigation.currentMemberId > 0) {
      checkIfUpdated();

      if (!isFormUpdatedRef?.current) {
        //formda değişiklik yoksa doğrudan geç
        dispatch(setStep(2));
      } else {
        memberModel.investigationMemberId = investigation.currentMemberId;
        memberModel.investigationTypeId =
          investigation?.investigationTypeId != 0
            ? investigation?.investigationTypeId
            : companyInfo?.investigationTypeId;

        var resultMember = await axioshelper(
          MethodType.PUT,
          BaseUrlType.Public_API,
          `investigations/${investigationId}/members`,
          memberModel
        );

        setLoadingAction(false);

        if (resultMember.status == "SUCCESS") {
          memberModel.birthdate = dayjs(values.member.birthdate).format(
            "DD/MM/YYYY"
          );
          memberModel.visaEntryDate = dayjs(values.member.visaEntryDate).format(
            "DD/MM/YYYY"
          );
          memberModel.visaExitDate = dayjs(values.member.visaExitDate).format(
            "DD/MM/YYYY"
          );
          memberModel.passportIssueDate = dayjs(
            values.member.passportIssueDate
          ).format("DD/MM/YYYY");
          memberModel.passportExpireDate = dayjs(
            values.member.passportExpireDate
          ).format("DD/MM/YYYY");

          var existingMember = investigation.members.find(
            (e) => e.investigationMemberId == investigation.currentMemberId
          );

          dispatch(setInvestigationMember(memberModel));
          if (resultMember?.data?.passportFileId > 0) {
            var files = existingMember?.files.filter((f) => f.id != 5);
            var passportFile = existingMember?.files.find(
              (e) => e.id == 5 && e?.state == 2
            );
            var pFile = {
              explanation: passportFile?.explanation,
              file: null,
              fileId: resultMember?.data?.passportFileId,
              id: passportFile?.id,
              state: 2,
            };

            files.push(pFile);
            dispatch(
              setMemberFiles({
                investigationMemberId: resultMember?.data?.id,
                files: files,
              })
            );
          }

          toast.success(
            t("site:successMessage", { operation: "Member update operation" })
          );

          dispatch(setStep(2));
        }
      }
    } else {
      var postModel = {
        channelId: 2,
        investigationId: investigationId,
        member: memberModel,
        investigationTypeId:
          investigation?.investigationTypeId != 0 &&
          investigation?.investigationTypeId != undefined
            ? investigation?.investigationTypeId
            : companyInfo?.investigationTypeId,
      };

      if (companyInfo?.investigationTypeId == 3) {
        var company = {
          companyName: companyInfo?.companyName,
          companyAddress: companyInfo?.companyAddress,
        };

        postModel.company = company;
      }

      var resultMember = await axioshelper(
        MethodType.POST,
        BaseUrlType.Public_API,
        `investigations/members`,
        postModel
      );

      if (resultMember.status == "SUCCESS") {
        memberModel.investigationMemberId =
          resultMember.data.investigationMemberId;
        toast.success(
          t("site:successMessage", { operation: "Member add operation" })
        );

        dispatch(
          setInvestigation({
            id: resultMember.data.investigationId,
            currentMemberId: resultMember.data.investigationMemberId,
            investigationTypeId:
              investigation?.investigationTypeId == null &&
              investigation?.investigationTypeId == 0 &&
              investigation?.investigationTypeId == undefined
                ? investigation?.investigationTypeId
                : companyInfo?.investigationTypeId,
          })
        );
        memberModel.birthdate = dayjs(memberModel.birthdate).format(
          "DD/MM/YYYY"
        );
        memberModel.visaEntryDate = dayjs(memberModel.visaEntryDate).format(
          "DD/MM/YYYY"
        );
        memberModel.visaExitDate = dayjs(memberModel.visaExitDate).format(
          "DD/MM/YYYY"
        );
        memberModel.passportIssueDate = dayjs(
          memberModel.passportIssueDate
        ).format("DD/MM/YYYY");
        memberModel.passportExpireDate = dayjs(
          memberModel.passportExpireDate
        ).format("DD/MM/YYYY");

        dispatch(setInvestigationMember(memberModel));
        if (resultMember?.data?.passportFileId > 0) {
          var files = [];

          var pFile = {
            explanation: t("site:passport"),
            file: null,
            fileId: resultMember?.data?.passportFileId,
            id: 5,
            state: 2,
          };

          files.push(pFile);
          dispatch(
            setMemberFiles({
              investigationMemberId: resultMember?.data?.investigationMemberId,
              files: files,
            })
          );
        }
        dispatch(setStep(2));
      }
    }
    setLoadingAction(false);
    setIsContinue(false);
  };

  const onChangeVisaEntryDate = (date) => {
    var computedVisaExitDate = new Date(date);
    computedVisaExitDate.setDate(
      computedVisaExitDate.getDate() + residenceDuration
    );

    form.setFieldsValue({
      member: {
        visaExitDate: dayjs(computedVisaExitDate),
      },
    });
  };

  const antIcon = (
    <LoadingOutlined
      style={{
        fontSize: 24,
      }}
      spin
    />
  );

  const AddFilesToList = async (id, file, explanation) => {
    if (file.status == "removed") {
      var updated = fileTypesRef.current.map((obj) =>
        obj.id == id ? { ...obj, state: 0, file: null } : obj
      );
      setFileTypes(updated);
      setCanProceed(false);
    } else {
      var base64File = await getBase64(file);
      var fModel = {
        uid: id,
        url: base64File,
        name: file.name,
        status: "done",
      };
      var updated = fileTypesRef.current?.map((obj) =>
        obj.id == id ? { ...obj, state: 1, file: fModel } : obj
      );
      setFileTypes(updated);
      uploadToServer(id, fModel, explanation);
    }
  };

  const handlePreview = async (id) => {
    var fileInfo = fileTypesRef.current.find((f) => f.id == id);
    if (fileInfo != null) {
      setPreviewImage(fileInfo.file.url || fileInfo.file.preview);
      setPreviewOpen(true);
      setPreviewTitle(t("site:passport"));
    }
  };

  const uploadToServer = async (id, filemodel, explanation) => {
    var data = filemodel.url.split(",").pop();
    var key = uuidv4();

    var model = {
      fileType: 5,
      fileName: filemodel.name,
      fileData: data,
      referenceKey: key,
    };

    setCanProceed(false);
    setLoading(true);

    var fileResult = await axioshelper(
      MethodType.POST,
      BaseUrlType.Public_API,
      `file/temp-files`,
      model
    );
    setLoading(false);
    if (fileResult.status == "SUCCESS") {
      var countryResult = await axioshelper(
        MethodType.GET,
        BaseUrlType.Public_API,
        `countries/${countryId}`,
        null
      );

      if (countryResult.status == "SUCCESS") {
        if (countryResult?.data?.isO3 != fileResult?.data?.nationality) {
          showSweetAlert(
            t("site:warning"),
            t("site:passportCountryWarning"),
            "warning"
          );
        }
      }

      setReferenceKey(key);
      var updated = fileTypesRef.current?.map((obj) =>
        obj.id == id ? { ...obj, state: 2, file: filemodel } : obj
      );
      setFileTypes(updated);
      toast.success(t("site:fileUploadSuccess", { fileType: explanation }));
      setCanProceed(true);

      var birthdate = dayjs(fileResult?.data.birthdate, "DD/MM/YYYY");
      var passportExpireDate = dayjs(
        fileResult?.data.passportExpireDate,
        "DD/MM/YYYY"
      );

      form.setFieldsValue({
        member: {
          name: toTransformCapitalize(fileResult?.data.name),
          surname: toTransformCapitalize(fileResult?.data.surname),
          birthdate: birthdate?.isValid() ? birthdate : null,
          passportNumber: fileResult?.data.passportNumber,
          passportExpireDate: passportExpireDate.isValid()
            ? passportExpireDate
            : null,
        },
      });
      setIsDisable(false);

      var filterWaiting = fileTypesRef.current.filter((f) => f.state != 2); //başarılı olmayanlar
      if (filterWaiting.length > 0) {
        setCanProceed(false);
      } else {
        setCanProceed(true);
      }
    } else {
      var updated = fileTypesRef.current?.map((obj) =>
        obj.id == id ? { ...obj, state: 3, file: filemodel } : obj
      );
      setFileTypes(updated);
      toast.error(fileResult?.data?.message);
    }
  };

  const initialForm = async () => {
    form.setFieldsValue({
      member: {
        name: null,
        surname: null,
        birthdate: null,
        fatherName: null,
        motherName: null,
        passportExpireDate: null,
        passportIssueDate: null,
        passportNumber: null,
        visaEntryDate:
          companyInfo?.investigationTypeId == 3
            ? dayjs(visaEntryDate, "DD/MM/YYYY")
            : null,
        visaExitDate:
          companyInfo?.investigationTypeId == 3
            ? dayjs(visaExitDateRef?.current, "DD/MM/YYYY")
            : null,
      },
    });
  };

  const checkIfUpdated = () => {
    var isUpdated = false;
    var existing = investigation.members.find(
      (e) => e.investigationMemberId == investigation.currentMemberId
    );
    var formValues = form.getFieldsValue();

    if (existing?.nationalityId != countryId) {
      isUpdated = true;
    } else if (existing?.visaTypeId != visaTypeId) {
      isUpdated = true;
    } else if (existing?.passportTypeId != passportTypeId) {
      isUpdated = true;
    } else if (
      existing?.name?.toUpperCase() != formValues.member.name?.toUpperCase()
    ) {
      isUpdated = true;
    } else if (
      existing?.surname?.toUpperCase() !=
      formValues.member.surname?.toUpperCase()
    ) {
      isUpdated = true;
    } else if (
      existing?.motherName?.toUpperCase() !=
      formValues.member.motherName?.toUpperCase()
    ) {
      isUpdated = true;
    } else if (
      existing?.fatherName?.toUpperCase() !=
      formValues.member.fatherName?.toUpperCase()
    ) {
      isUpdated = true;
    } else if (
      existing?.passportNumber?.toUpperCase() !=
      formValues.member.passportNumber?.toUpperCase()
    ) {
      isUpdated = true;
    } else if (
      existing?.passportExpireDate !=
      dayjs(formValues.member.passportExpireDate).format("DD/MM/YYYY")
    ) {
      isUpdated = true;
    } else if (
      existing?.passportIssueDate !=
      dayjs(formValues.member.passportIssueDate).format("DD/MM/YYYY")
    ) {
      isUpdated = true;
    } else if (
      existing?.birthdate !=
      dayjs(formValues.member.birthdate).format("DD/MM/YYYY")
    ) {
      isUpdated = true;
    } else if (
      existing?.visaEntryDate !=
      dayjs(formValues.member.visaEntryDate).format("DD/MM/YYYY")
    ) {
      isUpdated = true;
    }
    setIsFormUpdated(isUpdated);
  };

  const toTransformCapitalize = (str) => {
    const englishLetters = /^[A-Za-z\s]+$/;

    // İngilizce karakterleri ve boşlukları kabul et
    const filteredStr = str
      .split("")
      .filter((char) => englishLetters.test(char))
      .join("");

    const titleCase = filteredStr
      .toLocaleLowerCase("en")
      .split(" ")
      .map((word) => {
        return word.charAt(0).toLocaleUpperCase("en") + word.slice(1);
      })
      .join(" ");

    return titleCase;
  };

  return (
    <div key={investigation?.currentMemberId}>
      <Modal
        open={previewOpen}
        title={previewTitle}
        footer={null}
        onCancel={handleCancel}
      >
        <img
          style={{
            width: "100%",
          }}
          src={previewImage}
        />
      </Modal>
      <div key={currentMemberId}>
        <Spin indicator={antIcon} spinning={loading}>
          {fileTypesRef?.current?.map((file) => (
            <div key={file.id}>
              {file.id == 5 ? (
                <div className="card bg-transparent" key={file.id}>
                  <div className="card-body">
                    <div className="d-flex">
                      <div
                        className={
                          file.state == 3
                            ? "col-10 align-middle border-danger"
                            : "col-10 align-middle"
                        }
                      >
                        <ImgCrop rotationSlider aspect={3 / 2}>
                          <Upload
                            key={file.id}
                            listType="picture-card"
                            onPreview={() => {
                              handlePreview(file.id);
                            }}
                            beforeUpload={(_file) => {
                              AddFilesToList(5, _file, file.explanation);
                              return false;
                            }}
                            onChange={(e) => {
                              if (e.file.status == "removed") {
                                setIsFormUpdated(true);
                                var updated = fileTypesRef.current.map((obj) =>
                                  obj.id == 5
                                    ? { ...obj, state: 0, file: null }
                                    : obj
                                );
                                setCanProceed(false);

                                var exitDate = form.getFieldValue([
                                  "member",
                                  "visaExitDate",
                                ]);
                                var entryDate = form.getFieldValue([
                                  "member",
                                  "visaEntryDate",
                                ]);

                                form.resetFields();

                                if (
                                  companyInfo?.investigationTypeId == 3 ||
                                  investigation?.investigationTypeId == 3
                                ) {
                                  form.setFieldsValue({
                                    member: {
                                      visaEntryDate: dayjs(
                                        entryDate,
                                        "DD/MM/YYYY"
                                      ),
                                      visaExitDate: dayjs(
                                        exitDate,
                                        "DD/MM/YYYY"
                                      ),
                                    },
                                  });
                                }

                                setFileTypes(updated);
                              }
                            }}
                            defaultFileList={
                              fileTypesRef?.current?.filter(
                                (f) => f.id == file.id && f.state == 2
                              ).length > 0
                                ? fileTypesRef?.current
                                    ?.filter((f) => f.id == file.id)
                                    ?.map((obj) => {
                                      return obj.file ?? null;
                                    })
                                : []
                            }
                            maxCount={1}
                            accept="image/png, image/jpeg"
                          >
                            {file.state == 0 ? uploadButton : null}
                          </Upload>
                        </ImgCrop>
                        <span className="align-middle pl-3">
                          {file.explanation.trim()}
                        </span>
                      </div>
                      <div className="col-2 text-end pt-1">
                        <CheckCircleOutlined
                          style={{
                            color:
                              file.state == 2
                                ? "green"
                                : file.state == 3
                                ? "red"
                                : "",
                            fontSize: "18px",
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}
            </div>
          ))}
        </Spin>
      </div>
      <Collapse className="slf-mt-12" ghost>
        <Panel header={t("site:passportInstruction")} key="1">
          <div className="row">
            <div className="col">
              <img src={"../../approved.png"}></img>
            </div>
            <div className="col">
              {" "}
              <img src={"../../tilted.png"}></img>
            </div>
            <div className="col">
              {" "}
              <img src={"../../small.png"}></img>
            </div>
            <div className="col">
              {" "}
              <img src={"../../blur.png"}></img>
            </div>
            <div className="col">
              {" "}
              <img src={"../../half.png"}></img>
            </div>
          </div>
          <div className="row mt-2">
            <div className="col-12">
              <p className="fw-bold mb-0"> {t("site:firstPage")}</p>
              <ul className="list-group list-group-flush">
                <li className="list-group-item bg-transparent">
                  {t("site:instructions_1")}
                </li>
                <li className="list-group-item bg-transparent">
                  {t("site:instructions_2")}
                </li>
                <li className="list-group-item bg-transparent">
                  {t("site:instructions_3")}
                </li>
                <li className="list-group-item bg-transparent">
                  {t("site:instructions_4")}
                </li>
                <li className="list-group-item bg-transparent">
                  {t("site:instructions_5")}
                </li>
                <li className="list-group-item bg-transparent">
                  {t("site:instructions_6")}
                </li>
                <li className="list-group-item bg-transparent">
                  {t("site:instructions_7")}
                </li>
              </ul>
            </div>
          </div>
        </Panel>
      </Collapse>
      <Divider className="slf-mt-12 slf-mb-11"></Divider>
      <div className="d-flex">
        <div className="col-12">
          <Form
            form={form}
            name="frm-addMember"
            labelCol={{
              span: 12,
            }}
            size="large"
            onFinish={onFinish}
            wrapperCol={{
              span: 32,
            }}
            layout="vertical"
          >
            <div className="slf-personal-info-flex-wrap">
              <div className="slf-personal-info-flex">
                <Form.Item
                  label={t("form:visaEntryDate")}
                  name={["member", "visaEntryDate"]}
                  rules={[{ required: true }]}
                >
                  <DatePicker
                    disabled={
                      companyInfo?.investigationTypeId == 3 ||
                      (investigation?.investigationTypeId != undefined &&
                        investigation?.investigationTypeId == 3)
                        ? true
                        : false
                    }
                    format={getDateFormat[router?.locale]}
                    onChange={onChangeVisaEntryDate}
                    style={{ width: "100%" }}
                    disabledDate={(current) => {
                      return current && current < new Date();
                    }}
                  />
                </Form.Item>

                <Form.Item
                  disabled
                  label={t("form:visaExitDate")}
                  name={["member", "visaExitDate"]}
                  rules={[{ required: true }]}
                >
                  <DatePicker
                    format={getDateFormat[router?.locale]}
                    disabled
                    style={{ width: "100%" }}
                    disabledDate={(current) => {
                      return current && current > new Date();
                    }}
                    value={visaExitDateRef?.current}
                  />
                </Form.Item>
                <Form.Item
                  name={["member", "passportNumber"]}
                  label={t("form:passport")}
                  rules={[
                    {
                      max: 50,
                      required: true,
                    },
                  ]}
                >
                  <Input maxLength={50} />
                </Form.Item>
                <Form.Item
                  label={t("form:passportExpireDate")}
                  name={["member", "passportExpireDate"]}
                  rules={[
                    {
                      required: true,
                    },
                    {
                      validator: (_, value) =>
                        value &&
                        dayjs(value).isSameOrAfter(
                          dayjs().add(180, "day").startOf("day")
                        )
                          ? Promise.resolve()
                          : Promise.reject(
                              new Error(t("form:passportExpireDateRuleMessage"))
                            ),
                    },
                  ]}
                >
                  <DatePicker
                    format={getDateFormat[router?.locale]}
                    style={{ width: "100%" }}
                    disabledDate={(current) => {
                      return current && current < dayjs().startOf("day");
                    }}
                  />
                </Form.Item>
                <Form.Item
                  name={["member", "passportIssueDate"]}
                  label={t("form:passportIssueDate")}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <DatePicker
                    format={getDateFormat[router?.locale]}
                    style={{ width: "100%" }}
                    disabledDate={(current) => {
                      return current && current > new Date();
                    }}
                  />
                </Form.Item>
              </div>
              <div className="slf-personal-info-flex">
                <Form.Item
                  name={["member", "name"]}
                  label={t("form:name")}
                  rules={[
                    {
                      required: true,
                      max: 50,
                    },
                  ]}
                >
                  <Input
                    maxLength={50}
                    onInput={(e) =>
                      (e.target.value = toTransformCapitalize(e.target.value))
                    }
                  />
                </Form.Item>
                <Form.Item
                  name={["member", "surname"]}
                  label={t("form:surname")}
                  rules={[
                    {
                      required: true,
                      max: 50,
                    },
                  ]}
                >
                  <Input
                    maxLength={50}
                    onInput={(e) =>
                      (e.target.value = toTransformCapitalize(e.target.value))
                    }
                  />
                </Form.Item>
                <Form.Item
                  label={t("form:birthdate")}
                  name={["member", "birthdate"]}
                  rules={[{ required: true }]}
                >
                  <DatePicker
                    format={getDateFormat[router?.locale]}
                    style={{ width: "100%" }}
                    disabledDate={(current) => {
                      return current && current > new Date();
                    }}
                  />
                </Form.Item>
                <Form.Item
                  name={["member", "motherName"]}
                  label={t("form:motherName")}
                  rules={[
                    {
                      max: 50,
                      required: true,
                    },
                  ]}
                >
                  <Input
                    maxLength={50}
                    onInput={(e) =>
                      (e.target.value = toTransformCapitalize(e.target.value))
                    }
                  />
                </Form.Item>
                <Form.Item
                  name={["member", "fatherName"]}
                  label={t("form:fatherName")}
                  rules={[
                    {
                      max: 50,
                    },
                  ]}
                >
                  <Input
                    maxLength={50}
                    onInput={(e) =>
                      (e.target.value = toTransformCapitalize(e.target.value))
                    }
                  />
                </Form.Item>
              </div>
            </div>

            <div className="d-flex mt-4">
              <div className="col-6">
                <Button
                  className="btn stepper-button-back"
                  size="large"
                  onClick={() => {
                    dispatch(setStep(0));
                  }}
                >
                  {t("site:back")}
                </Button>
              </div>
              {canProceed ? (
                <div className="col-6 text-end">
                  <Button
                    disabled={isContinue}
                    size="large"
                    htmlType="submit"
                    className="btn stepper-button-next"
                    loading={loadingAction}
                  >
                    {t("site:continue")}
                  </Button>
                </div>
              ) : null}
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default PersonalInformation;
