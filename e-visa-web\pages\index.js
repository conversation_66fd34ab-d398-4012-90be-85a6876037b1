import Container from "react-bootstrap/Container";
import Accordion from "react-bootstrap/Accordion";
import Image from "next/image";
import useTranslation from "next-translate/useTranslation";
import Link from "next/link";
import {
  But<PERSON>,
  Form
} from "antd";
import {
  IconGroup460,
  IconGroup460Inside,
  IconGroup457,
  IconGroup457Inside,
  IconGroup458,
  IconGroup458Inside,
  IconMap,
  IconRedArrowRight,
} from "../utils/icons";
import { useRouter } from "next/router";
import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useState } from "react";
import CheckAvailability from "../components/check-availability";

export default function Home() {
  const { data: session, status } = useSession();
  const [authenticated, setAuthenticated] = useState(false);
  const [faqButton, setFaqButton] = useState(0);
  const { t } = useTranslation();
  const [form] = Form.useForm();
  var router = useRouter();

  useEffect(() => {
    if (status == "authenticated") {
      setAuthenticated(true);
      console.log("session:", session)
    }
  }, [status]);

  const getFaq = (index) => {
    const faq = [
      [
        { header: t("site:faqBefore1"), body: t("site:faqBefore1Desc") },
        { header: t("site:faqBefore2"), body: t("site:faqBefore2Desc") },
        { header: t("site:faqBefore3"), body: t("site:faqBefore3Desc") },
        { header: t("site:faqBefore4"), body: t("site:faqBefore4Desc") },
      ],
      [
        { header: t("site:faqDuring1"), body: t("site:faqDuring1Desc") },
        { header: t("site:faqDuring2"), body: t("site:faqDuring2Desc") },
        { header: t("site:faqDuring3"), body: t("site:faqDuring3Desc") },
        { header: t("site:faqDuring4"), body: t("site:faqDuring4Desc") },
      ],
      [
        { header: t("site:faqAfter1"), body: t("site:faqAfter1Desc") },
        { header: t("site:faqAfter2"), body: t("site:faqAfter2Desc") },
        { header: t("site:faqAfter3"), body: t("site:faqAfter3Desc") },
        { header: t("site:faqAfter4"), body: t("site:faqAfter4Desc") },
      ],
    ];

    return faq[faqButton][index];
  };

  return (
    <>
      <section className={`slf-section-1 ${authenticated ? "slf-h-600" : ""}`}>
        <div className="slf-section-1-white-mask"></div>
        <Container
          className={`slf-container ${authenticated ? "slf-h-600" : ""}`}
        >
          <Image
            className="slf-section-1-3"
            src="/img/landing/section-1-3.png"
            width={588}
            height={805}
            alt="human"
          ></Image>
          <h1
            className="slf-section-1-h1"
            dangerouslySetInnerHTML={{ __html: t("site:section1H1") }}
          ></h1>
          <h2 className="slf-section-1-h2">{t("site:section1H2")}</h2>

          {authenticated ? (
            <div className="slf-section-1-text-column">
              <h3 className="slf-section-1-h3">
                {t("site:hello")} {session?.user?.name}
              </h3>
              <Button
                onClick={() => {
                  router.push("/application/navigation-page");
                }}
                className="slf-section-1-action-button slf-w-fit"
                type="primary"
              >
                {t("site:applyNow")}
              </Button>
            </div>
          ) : (
            <CheckAvailability></CheckAvailability>
          )}
        </Container>
      </section>

      <section className="slf-section-2">
        <Container className="slf-container">
          <div className="slf-section-2-for-order">
            <div className={`${authenticated ? "order-3" : "order-1"}`}>
              <h2 className="slf-section-2-1-h2">{t("site:whyShouldEVisa")}</h2>
              <h3
                className="slf-section-2-1-h3"
                dangerouslySetInnerHTML={{
                  __html: t("site:whyShouldEVisaDesc"),
                }}
              ></h3>
              <div className="slf-section-2-1-rows">
                <div className="slf-section-2-1-row">
                  <IconGroup460 className={"slf-section-2-1-row-image-1"} />
                  <IconGroup460Inside
                    className={"slf-section-2-1-row-image-2"}
                  />
                  <h4 className="slf-section-2-1-row-h4">
                    {t("site:quickAndEasy")}
                  </h4>
                  <p className="slf-section-2-1-row-p">
                    {t("site:quickAndEasyDesc")}
                  </p>
                </div>
                <div className="slf-section-2-1-row">
                  <IconGroup457 className={"slf-section-2-1-row-image-1"} />
                  <IconGroup457Inside
                    className={"slf-section-2-1-row-image-2 top-49px"}
                  />
                  <h4 className="slf-section-2-1-row-h4">
                    {t("site:trustworthy")}
                  </h4>
                  <p className="slf-section-2-1-row-p">
                    {t("site:trustworthyDesc")}
                  </p>
                </div>
                <div className="slf-section-2-1-row">
                  <IconGroup458 className={"slf-section-2-1-row-image-1"} />
                  <IconGroup458Inside
                    className={"slf-section-2-1-row-image-2 top-47px"}
                  />
                  <h4 className="slf-section-2-1-row-h4">
                    {t("site:onAllPlatforms")}
                  </h4>
                  <p className="slf-section-2-1-row-p">
                    {t("site:onAllPlatformsDesc")}
                  </p>
                </div>
              </div>
            </div>
            <div
              id="mobileApp"
              className={`slf-section-2-2-fix-height ${
                authenticated ? "order-1" : "order-2"
              }`}
            >
              <div className="slf-section-2-2">
                <Container className="slf-container">
                  <Image
                    className="slf-section-2-2-2"
                    src="/img/landing/section-2-2-2.png"
                    width={334}
                    height={660}
                    alt="phone1"
                  ></Image>
                  <Image
                    className="slf-section-2-2-3"
                    src="/img/landing/section-2-2-3.png"
                    width={414}
                    height={844}
                    alt="phone2"
                  ></Image>
                  <div className="slf-section-2-2-right">
                    <h2
                      className="slf-section-2-2-right-h2"
                      dangerouslySetInnerHTML={{
                        __html: t("site:forQuickApp"),
                      }}
                    ></h2>
                    <h3 className="slf-section-2-2-right-h3">
                      {t("site:mobilAppSpeed")}
                    </h3>
                    <div className="slf-section-2-2-right-images">
                      <Image
                        className="slf-section-2-2-right-app-store"
                        src="/img/landing/app-store.png"
                        width={196}
                        height={62}
                        alt="app-store"
                      ></Image>
                      <Image
                        className="slf-section-2-2-right-play-store"
                        src="/img/landing/play-store.png"
                        width={196}
                        height={62}
                        alt="play-store"
                      ></Image>
                    </div>
                  </div>
                </Container>
              </div>
            </div>
            <div
              className={`${authenticated ? "order-2" : "slf-display-none"}`}
            >
              <h2 className="slf-section-2-1-h2 slf-mt-177px">
                {t("site:whereVisitTurkey")}
              </h2>
              <h3
                className="slf-section-2-1-h3"
                dangerouslySetInnerHTML={{
                  __html: t("site:whereVisitTurkeyDesc"),
                }}
              ></h3>
              <div className="slf-section-2-1-rows">
                <div className="slf-section-2-1-row">
                  <Image
                    src="/img/landing/kapadokya.jpg"
                    width={416}
                    height={280}
                    style={{
                      objectFit: "contain",
                      width: "100%",
                      borderRadius: "10px 10px 0 0",
                    }}
                    alt="kapadokya"
                  ></Image>
                  <h4 className="slf-section-2-3-row-h4">
                    {t("site:cappadociaRuins")}
                  </h4>
                  <div className="slf-section-2-3-row-subtitle">
                    <IconMap />
                    <span>Nevşehir</span>
                  </div>
                  <p className="slf-section-2-3-row-desc">
                    {t("site:cappadociaRuinsDesc")}
                  </p>
                  <Link href={""} className="slf-section-2-3-row-link">
                    {t("site:seeAll")}
                    <IconRedArrowRight className={"ml-10px"} />
                  </Link>
                </div>
                <div className="slf-section-2-1-row">
                  <Image
                    src="/img/landing/efes.jpg"
                    width={416}
                    height={280}
                    style={{
                      objectFit: "contain",
                      width: "100%",
                      borderRadius: "10px 10px 0 0",
                    }}
                    alt="efes"
                  ></Image>
                  <h4 className="slf-section-2-3-row-h4">
                    {t("site:ephesusAncientCity")}
                  </h4>
                  <div className="slf-section-2-3-row-subtitle">
                    <IconMap />
                    <span>İzmir</span>
                  </div>
                  <p className="slf-section-2-3-row-desc">
                    {t("site:ephesusAncientCityDesc")}
                  </p>
                  <Link href={""} className="slf-section-2-3-row-link">
                    {t("site:seeAll")}
                    <IconRedArrowRight className={"ml-10px"} />
                  </Link>
                </div>
                <div className="slf-section-2-1-row">
                  <Image
                    src="/img/landing/sumela.jpg"
                    width={416}
                    height={280}
                    style={{
                      objectFit: "contain",
                      width: "100%",
                      borderRadius: "10px 10px 0 0",
                    }}
                    alt="sumela"
                  ></Image>
                  <h4 className="slf-section-2-3-row-h4">
                    {t("site:sumelaMonastery")}
                  </h4>
                  <div className="slf-section-2-3-row-subtitle">
                    <IconMap />
                    <span>Trabzon</span>
                  </div>
                  <p className="slf-section-2-3-row-desc">
                    {t("site:sumelaMonasteryDesc")}
                  </p>
                  <Link href={""} className="slf-section-2-3-row-link">
                    {t("site:seeAll")}
                    <IconRedArrowRight className={"ml-10px"} />
                  </Link>
                </div>
              </div>
            </div>
            <div
              className={`slf-section-2-4-fix-height ${
                authenticated ? "order-4" : "order-3 mt-77px"
              }`}
            >
              <div className="slf-section-2-4" id="howGetVisa">
                <Container className="slf-container">
                  <div className="slf-section-2-4-row">
                    <div>
                      <h2 className="slf-section-2-4-row-h2">
                        {t("site:howGetVisa")}
                      </h2>
                      <p
                        className="slf-section-2-4-row-p"
                        dangerouslySetInnerHTML={{
                          __html: t("site:howGetVisaDesc"),
                        }}
                      ></p>
                      <Button
                        className="slf-section-2-4-row-action-button"
                        type="primary"
                        onClick={() => {
                          authenticated
                            ? router.push("/application/navigation-page")
                            : router.push("/login");
                        }}
                      >
                        {t("site:quickApplication")}
                      </Button>
                    </div>
                    <div>
                      <Image
                        src="/img/landing/section-2-4-2.png"
                        width={635}
                        height={431}
                        alt="video"
                      ></Image>
                    </div>
                  </div>
                </Container>
              </div>
            </div>
          </div>
        </Container>
      </section>

      <section className="slf-section-3" id="frequentlyAskedQuestions">
        <Container className="slf-container">
          <h2 className="slf-section-3-h2">
            {t("site:frequentlyAskedQuestions")}
          </h2>
          <div className="slf-section-3-flex">
            <Button
              className={`slf-section-3-button ${
                faqButton == 0 ? "active" : ""
              }`}
              type="primary"
              onClick={() => setFaqButton(0)}
            >
              {t("site:beforeApplication")}
            </Button>
            {authenticated && (
              <>
                <Button
                  className={`slf-section-3-button ${
                    faqButton == 1 ? "active" : ""
                  }`}
                  type="primary"
                  onClick={() => setFaqButton(1)}
                >
                  {t("site:duringApplication")}
                </Button>
                <Button
                  className={`slf-section-3-button ${
                    faqButton == 2 ? "active" : ""
                  }`}
                  type="primary"
                  onClick={() => setFaqButton(2)}
                >
                  {t("site:afterApplication")}
                </Button>
              </>
            )}
          </div>
          <Accordion className="slf-section-3-accordion" defaultActiveKey="1">
            <Accordion.Item eventKey="0">
              <Accordion.Header>{getFaq(0)?.header}</Accordion.Header>
              <Accordion.Body>{getFaq(0)?.body}</Accordion.Body>
            </Accordion.Item>
            <Accordion.Item eventKey="1">
              <Accordion.Header>{getFaq(1)?.header}</Accordion.Header>
              <Accordion.Body>{getFaq(1)?.body}</Accordion.Body>
            </Accordion.Item>
            <Accordion.Item eventKey="2">
              <Accordion.Header>{getFaq(2)?.header}</Accordion.Header>
              <Accordion.Body>{getFaq(2)?.body}</Accordion.Body>
            </Accordion.Item>
            <Accordion.Item eventKey="3">
              <Accordion.Header>{getFaq(3)?.header}</Accordion.Header>
              <Accordion.Body>{getFaq(3)?.body}</Accordion.Body>
            </Accordion.Item>
          </Accordion>
        </Container>
      </section>
    </>
  );
}
