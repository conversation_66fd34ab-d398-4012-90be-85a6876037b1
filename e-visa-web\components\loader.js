import { Spin } from "antd";
import { useSelector } from "react-redux";
import { LoadingOutlined } from "@ant-design/icons";

const Loader = ({ children }) => {
  const loader = useSelector((state) => state.loader.value);
  var randomColor = Math.floor(Math.random() * 16777215).toString(16);
  const antIcon = (
    <LoadingOutlined
      style={{ fontSize: 36, fontWeight: "bold", color: `#${randomColor}` }}
    />
  );

  return (
    <Spin indicator={antIcon} spinning={loader}>
      {children}
    </Spin>
  );
};

export default Loader;
