import axioshelper, { BaseUrlType, MethodType } from "./axioshelper";

  const api = {
    GetCountries: async () => 
    await axioshelper(MethodType.GET,BaseUrlType.Public_API, "countries", null),

    GetDefinitions: async(groupCode)=>
    await axioshelper(MethodType.GET,BaseUrlType.Public_API, `definitions?groupCode=${groupCode}`, null),

    GetCountryFiles: async(countryId)=>
    await axioshelper(MethodType.GET,BaseUrlType.Public_API, `country/${countryId}/file-types`, null),

    GetPassportTypes: async()=>
    await axioshelper(MethodType.GET,BaseUrlType.Public_API, `passport-types`, null),

    GetCountryPassportTypes: async(countryId)=>
    await axioshelper(MethodType.GET,BaseUrlType.Public_API, `passport/${countryId}/available-passports`, null ),

    GetVisaTypes: async()=>
    await axioshelper(MethodType.GET,BaseUrlType.Public_API, `visa-types`, null),
  }

  export default api;