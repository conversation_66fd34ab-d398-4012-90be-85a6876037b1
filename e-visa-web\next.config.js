const nextTranslate = require("next-translate");

/** @type {import('next').NextConfig} */
module.exports = nextTranslate({
  webpack: (config, { isServer, webpack }) => {
    config.experiments = { ...config.experiments, topLevelAwait: true };

    return config;
  },
  env: {
    NEXT_PRIVATE_API_URL: process.env.NEXT_PRIVATE_API_URL,
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_CMS_URL: process.env.NEXT_PUBLIC_CMS_URL,
    IdentityServer4_Issuer: process.env.IdentityServer4_Issuer,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL
  },
  // experimental: {
  //   outputStandalone: true,
  //   //optimizeCss: true,
  //   //output: 'standalone'
  // },
  // publicRuntimeConfig: {
  //   // Will be available on both server and client
  //   staticFolder: '/static',
  // },

  // output: {
  //   filename: '[name].bundle.js',
  //   path: path.resolve(__dirname, 'dist'),
  //   clean: true,
  // },
  output: "standalone",
});
