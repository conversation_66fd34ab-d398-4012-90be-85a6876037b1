import React from "react";
import { useRouter } from "next/router";
import { useRef, useEffect, useState } from "react";
import useTranslation from "next-translate/useTranslation";
import showSweetAlert from "../utils/sweetAlert";
import axioshelper, { BaseUrlType, MethodType } from "../pages/api/axioshelper";

export async function getServerSideProps(context) {
  return {
    props: {
      code: context?.query?.code ?? null,
      userId: context?.query?.userId ?? null,
    },
  };
}

const ConfirmEmail = ({ userId, code }) => {
  const { t } = useTranslation();
  var router = useRouter();
  const inputRef = useRef(null);

  useEffect(() => {
    confirmEmail();
  }, []);

  const confirmEmail = async () => {
    var result = await axioshelper(
      MethodType.POST,
      BaseUrlType.Public_API,
      `user/confirm-email`,
      {
        userId: userId,
        code: code,
      }
    );
    if (result?.status == "SUCCESS") {
      showSweetAlert(
        t("site:success"),
        t("site:confirmSuccess"),
        "success",
        3000
      ).then(() => {
        router.push("/login");
      });
    } else {
      showSweetAlert(t("site:error"), result.data?.message, "error");
    }
  };
  return <></>;
};

export default ConfirmEmail;
