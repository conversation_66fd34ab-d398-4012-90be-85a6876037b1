import axiosConfig from "./axiosConfig";
import Router from "next/router";
import { getSession } from "next-auth/react";

export const enum BaseUrlType {
  Public_API,
  CMS_API
}

export const enum MethodType {
  GET,
  POST,
  PUT,
  DELETE,
}

const axioshelper = async (
  methodType: MethodType,
  baseurlType: BaseUrlType | string,
  url,
  data
) => {
  var baseurlStr = "";
  const session = await getSession()
  if (session) {
    axiosConfig.defaults.headers.common.Authorization = `Bearer ${session["accessToken"]}`;
  }

  if (Router.locale == "en") {
    axiosConfig.defaults.headers["Accept-Language"] = "en-US";
  } else {
    axiosConfig.defaults.headers["Accept-Language"] = "tr-TR";
  }
  
   if (baseurlType == BaseUrlType.Public_API) {
    baseurlStr = process.env.NEXT_PUBLIC_API_URL;
  } else if (baseurlType == BaseUrlType.CMS_API) {
    baseurlStr = process.env.NEXT_PUBLIC_API_URL;
  } else {
    baseurlStr = baseurlType;
  }

  if (methodType == MethodType.GET) {
    return await axiosConfig.get(`${baseurlStr}/${url}`);
  } else if (methodType == MethodType.POST) {
    return await axiosConfig.post(`${baseurlStr}/${url}`, data);
  }
  else if (methodType == MethodType.DELETE) {
    return await axiosConfig.delete(`${baseurlStr}/${url}`);
  }
  else if (methodType == MethodType.PUT) {
    return await axiosConfig.put(`${baseurlStr}/${url}`, data);
  }
}

export default axioshelper;
