<svg width="1440" height="1160" viewBox="0 0 1440 1160" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_233_3410)">
<rect width="1440" height="1160" fill="white"/>
<g opacity="0.6">
<g opacity="0.4" filter="url(#filter0_f_233_3410)">
<ellipse cx="-24.4983" cy="671.652" rx="498.792" ry="215.07" transform="rotate(-30 -24.4983 671.652)" fill="#7547D7"/>
</g>
<g opacity="0.3" filter="url(#filter1_f_233_3410)">
<ellipse cx="166.204" cy="859.803" rx="227.094" ry="215.07" transform="rotate(-30 166.204 859.803)" fill="#B48576"/>
</g>
</g>
<g opacity="0.4" filter="url(#filter2_f_233_3410)">
<ellipse cx="1401.5" cy="255.652" rx="498.792" ry="215.07" transform="rotate(-30 1401.5 255.652)" fill="#7547D7"/>
</g>
<g opacity="0.4" filter="url(#filter3_f_233_3410)">
<ellipse cx="1330.2" cy="673.803" rx="227.094" ry="215.07" transform="rotate(-30 1330.2 673.803)" fill="#B48576"/>
</g>
</g>
<defs>
<filter id="filter0_f_233_3410" x="-609.754" y="220.362" width="1170.51" height="902.58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="70" result="effect1_foregroundBlur_233_3410"/>
</filter>
<filter id="filter1_f_233_3410" x="-197.986" y="501.633" width="728.381" height="716.339" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="70" result="effect1_foregroundBlur_233_3410"/>
</filter>
<filter id="filter2_f_233_3410" x="816.246" y="-195.638" width="1170.51" height="902.58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="70" result="effect1_foregroundBlur_233_3410"/>
</filter>
<filter id="filter3_f_233_3410" x="966.014" y="315.633" width="728.381" height="716.339" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="70" result="effect1_foregroundBlur_233_3410"/>
</filter>
<clipPath id="clip0_233_3410">
<rect width="1440" height="1160" fill="white"/>
</clipPath>
</defs>
</svg>
