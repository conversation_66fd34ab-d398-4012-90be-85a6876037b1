# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
- master

pool:
  name: 'PortalPool'

steps:
- task: NodeTool@0
  inputs:
    versionSpec: '18.x'
  displayName: 'Install Node.js'

- task: UseNode@1
  inputs:
    version: '18.x'
    checkLatest: true
    command: 'npx next -h'
    
- task: Npm@1
  inputs:
    command: 'install'
    workingDir: 'e-visa-web\'

- task: Npm@1
  inputs:
    command: 'custom'
    workingDir: 'e-visa-web\'
    customCommand: 'run build'

- task: ArchiveFiles@2
  inputs:
    rootFolderOrFile: 'e-visa-web\.next\standalone\'
    includeRootFolder: false
    archiveType: 'zip'
    archiveFile: 'e-visa-web/drop/e-visa-web-drop.zip'
    replaceExistingArchive: true

- task: PublishBuildArtifacts@1
  displayName: 'Publish artifact'
  inputs:
    PathtoPublish: 'e-visa-web/drop'
    ArtifactName: 'e-visa-web-drop'
