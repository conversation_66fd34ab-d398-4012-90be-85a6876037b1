import axioshelper, { BaseUrlType, MethodType } from "./axioshelper";

let sorgulandi = false;
let groupMin = 0;
let groupMax = 0;
let familyMax = 0;

const initializeConsts = async () => {
  if (!sorgulandi) {
    var res = await axioshelper(
      MethodType.GET,
      BaseUrlType.Public_API,
      "regulations",
      null
    );
    if (res?.status == "SUCCESS") {
      groupMin = parseInt(res?.data?.find((f) => f.name == "GroupMinCount")?.value);
      groupMax = parseInt(res?.data?.find((f) => f.name == "GroupMaxCount")?.value);
      familyMax = parseInt(res?.data?.find((f) => f.name == "FamilyMaxCount")?.value);
      sorgulandi = true;
    }
  }
};

export { initializeConsts, groupMin, groupMax, familyMax };
