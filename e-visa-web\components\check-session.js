import { signIn, signOut, useSession } from "next-auth/react";
import { useEffect } from "react";
import showSweetAlertConfirm from "../utils/sweetAlertConfirm";
import useStateRef from "react-usestateref";
import useTranslation from "next-translate/useTranslation";
import { useRouter } from "next/router";

const CheckSession = () => {
  const { data: session, update, status } = useSession();
  const [sessionDialog, setSessionDialog, sessionDialogRef] = useStateRef(true);
  const { t } = useTranslation();
  var router = useRouter();

  const Check = async () => {
    if (status != "loading") {
      if (status == "authenticated") {
        var expires = session.expires * 1000;
        var expireControl = expires - 30000; //30 sn önce haber versin;
        var dtNow = new Date().getTime();
        if (dtNow > expires) {
           signOut();
        }
        if (dtNow > expireControl) {
          if (sessionDialogRef?.current) {
            showSweetAlertConfirm(
              t("site:sessionExpire"),
              t("site:sessionExpireText"),
              "warning",
              9000
            ).then(async (result) => {
              if (result.isConfirmed) {
               await signIn("is-refresh", {
                  redirect: false
                });
              } else {
                setSessionDialog(false);
              }
            });
          }
        }
      }
    }
  };
  useEffect(() => {
    if (status == "authenticated") {
      var expires = session.expires * 1000;
      var dtNow = new Date().getTime();
      if (dtNow > expires) {
       signOut();
      }
      const interval = setInterval(() => {
        Check();
      }, 10000);
      return () => {
        clearInterval(interval);
      };
    }
  }, [status, session, router?.locale]);

  return ( <></> );
};

export default CheckSession;
