import Image from "next/image";
import useTranslation from "next-translate/useTranslation";
import { useRouter } from "next/router";

const UserEnter = ({ children }) => {
  const { t } = useTranslation();
  const route = useRouter();

  return (
    <div className="main">
      <div className="left">
        <div className="left-bottom-mask"></div>
        <div className="left-group">
          <Image
            className="left-group-58"
            src="/images/group-58-1.png"
            alt="group-58"
            width="226"
            height="300"
          />
          <Image
            className="left-group-62"
            src="/images/frame-62-1.png"
            alt="frame-62"
            width="133"
            height="133"
          />
          <Image
            className="left-group-59"
            src="/images/group-59-1.png"
            alt="group-59"
            width="282"
            height="220"
          />
          <Image
            className="left-group-3"
            src="/images/mockup-3-1.png"
            alt="mockup-3"
            width="286"
            height="568"
          />
          <Image
            className="left-group-60"
            src="/images/group-60-1.png"
            alt="group-60"
            width="340"
            height="108"
          />
          <span className="left-group-tech-text">
            {t("site:faceIdentificationTechnology")}
          </span>
          {route.locale == "tr" ? (
            <>
              <Image
                className="left-group-app-store"
                src="/images/app-store.png"
                alt="app-store"
                width="197"
                height="52"
              />
              <Image
                className="left-group-google-play"
                src="/images/google-play.png"
                alt="google-play"
                width="177"
                height="52"
              />
            </>
          ) : (
            <>
              <Image
                className="left-group-app-store"
                src="/img/landing/app-store.png"
                width={196}
                height={62}
                alt="app-store"
              ></Image>
              <Image
                className="left-group-google-play"
                src="/img/landing/play-store.png"
                width={196}
                height={62}
                alt="play-store"
              ></Image>
            </>
          )}
        </div>
      </div>
      <div className="right">
        <div className="right-wrap">
          <div className="right-body">{children}</div>
        </div>
      </div>
    </div>
  );
};

export default UserEnter;
