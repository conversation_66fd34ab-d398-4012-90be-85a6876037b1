import Precondition from "../../components/application/precondition";
import PersonalInformation from "../../components/application/personal-info";
import useTranslation from "next-translate/useTranslation";
import { Divider, Steps } from "antd";
import MemberFiles from "../../components/application/member-files";
import Payment from "../../components/application/payment";
import Preview from "../../components/application/preview";
import { useSelector } from "react-redux";
import {
  CreditCardOutlined,
  FileProtectOutlined,
  GlobalOutlined,
  ReadOutlined,
  SolutionOutlined,
} from "@ant-design/icons";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { toast } from "react-toastify";
import { initializeConsts } from "../api/regulations";

const NewApplication = () => {
  React.useLayoutEffect = React.useEffect;
  const currentStep = useSelector((state) => state.stepper.value);
  var investigation = useSelector((state) => state.application.investigation);

  const { t } = useTranslation();
  const router = useRouter();
  const items = [
    {
      title: t("site:precondition"),
      icon: <GlobalOutlined style={{ fontSize: 35 }} />,
    },
    {
      title: t("site:personalInfo"),
      icon: <SolutionOutlined style={{ fontSize: 35 }} />,
    },
    {
      title: t("site:files"),
      icon: <FileProtectOutlined style={{ fontSize: 35 }} />,
    },
    {
      title: t("site:preview"),
      icon: <ReadOutlined style={{ fontSize: 35 }} />,
    },
    {
      title: t("site:payment"),
      icon: <CreditCardOutlined style={{ fontSize: 35 }} />,
    },
  ];
  useEffect(() => {
    var typeList = [1, 2, 3];
    if (investigation?.id == 0) {
      //yeni kayıt
      if (typeList.indexOf(parseInt(router.query?.investigationTypeId)) == -1) {
        toast.warning(t("site:invalidUrl"), { autoClose: 2000 });
        setTimeout(() => {
          router.push("/application/navigation-page");
        }, 500);
      }
    }
    setVariables();
  });

  const setVariables = async () => {
    await initializeConsts();
  };

  return (
    <>
      <>
        <div className="container slf-container">
          <div className="card card-item mt-3 mb-5">
            <div className="card-body">
              <div className="pt-4">
                <Steps
                  current={currentStep}
                  labelPlacement="vertical"
                  items={items}
                />
              </div>
              <Divider></Divider>

              <div
                style={{
                  display:
                    currentStep == 0 || currentStep == undefined
                      ? "block"
                      : "none",
                }}
              >
                <Precondition />
              </div>
              <div style={{ display: currentStep == 1 ? "block" : "none" }}>
                <PersonalInformation />
              </div>
              <div style={{ display: currentStep == 2 ? "block" : "none" }}>
                <MemberFiles />
              </div>
              <div style={{ display: currentStep == 3 ? "block" : "none" }}>
                <Preview />
              </div>
              <div style={{ display: currentStep == 4 ? "block" : "none" }}>
                <Payment />
              </div>
            </div>
          </div>
        </div>
      </>
    </>
  );
};

export default NewApplication;
