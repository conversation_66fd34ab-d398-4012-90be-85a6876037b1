import React from "react";
import { Button, Form, Input } from "antd";
import { useRouter } from "next/router";
import { useRef, useEffect, useState } from "react";
import useTranslation from "next-translate/useTranslation";
import Link from "next/link";
import showSweetAlert from "../utils/sweetAlert";
import axioshelper, { BaseUrlType, MethodType } from "../pages/api/axioshelper";
import UserEnter from "../components/userEnter";
// import ReCAPTCHA from "react-google-recaptcha";

const ForgotPassword = () => {
  const [loadingAction, setLoadingAction] = useState(false);
  const [disabledAction, setDisabledAction] = useState(false);
  const [reCaptchaToken, setReCaptchaToken] = useState(null);
  const [form] = Form.useForm();
  const { t } = useTranslation();
  var router = useRouter();
  const inputRef = useRef(null);

  useEffect(() => {
    setTimeout(() => {
      inputRef.current?.focus();
    }, 10);
  }, []);

  const onOk = () => {
    form
      .validateFields()
      .then(async (values) => {
        setLoadingAction(true);
        const result = await axioshelper(
          MethodType.POST,
          BaseUrlType.Public_API,
          `user/forgot-password`,
          values
        );
        setLoadingAction(false);
        if (result?.status == "SUCCESS") {
          showSweetAlert(
            t("site:success"),
            t("site:forgotPasswordSuccess"),
            "success"
          ).then(() => {
            router.push("/reset-password");
          });
        } else {
          showSweetAlert(t("site:error"), result.data.message, "error");
        }
      })
      .catch(() => {});
  };

  // const reCaptchaOnChange = (value) => {
  //   //-- süre aşımında null geliyor butonu kilitliyoruz ki ilerleyemesin
  //   if (value === null) {
  //     setDisabledAction(true);
  //   } else {
  //     //-- içi doluysa token geldi olarak varsayıyoruz bu kodu BE de doğrulayacağız
  //     setDisabledAction(false);
  //   }
  //   setReCaptchaToken(value);
  // };

  return (
    <UserEnter>
      <h1>{t("site:welcome")}</h1>
      <p className="right-body-welcome">{t("site:forgotPassEVisa")}</p>
      <Form form={form} layout="vertical">
        <Form.Item
          className="form-group"
          label={t("site:email")}
          name="email"
          rules={[
            {
              required: true,
              type: "email",
            },
          ]}
        >
          <Input
            ref={inputRef}
            className="form-control"
            onPressEnter={() =>
              form
                .validateFields()
                .then(() => {
                  onOk();
                })
                .catch(() => {})
            }
          />
        </Form.Item>
        {/* <ReCAPTCHA
          className="slf-recaptcha"
          hl={router.locale}
          sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY}
          onChange={reCaptchaOnChange}
        /> */}
        <Button
          type="primary"
          className="btn-submit"
          onClick={() => onOk()}
          loading={loadingAction}
          disabled={disabledAction}
        >
          {t("form:send")}
        </Button>
      </Form>
      <p className="right-body-register mt-24">
        {t("site:registerDesc")}{" "}
        <Link href="/register" className="right-body-register-link">
          {t("site:register")}
        </Link>
      </p>
    </UserEnter>
  );
};

export default ForgotPassword;
