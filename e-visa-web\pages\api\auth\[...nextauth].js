import NextAuth from "next-auth";
import IdentityServer<PERSON><PERSON>rovider from "next-auth/providers/identity-server4";
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials";
import { decode } from "../../../utils/token-parser";
import { getSession } from "next-auth/react";

export const NextAuthOptions = {
  session: {
    strategy: "jwt",
  },
  providers: [
    IdentityServer4Provider({
      id: "identity-server4",
      name: "IdentityServer4",
      issuer: process.env.IdentityServer4_Issuer,
      clientId: process.env.IdentityServer4_CLIENT_ID,
      clientSecret: process.env.IdentityServer4_CLIENT_SECRET,
      type: "oauth",
      wellKnown: `${process.env.IdentityServer4_Issuer}/.well-known/openid-configuration`,
      checks: ["pkce", "state"],
      idToken: true,
      authorizationUrl: `https://${process.env.IdentityServer4_Issuer}/authorize?response_type=code&prompt=login`,
      userinfo: {
        url: `${process.env.IdentityServer4_Issuer}/connect/userinfo`,
        async request({ client, tokens }) {
          const profile = await client.userinfo(tokens);
          return profile;
        },
      },
      authorization: {
        params: {
          prompt: "login",
          scope:
            "openid offline_access email roles profile userid IdentityServerApi",
        },
      },

      profile(profile, token) {
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: null,
        };
      },
    }),
    CredentialsProvider({
      id: "is-credential",
      name: "is-credential",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials, req) {
        //-- Google ReCaptcha token doğrulama işlemi
        // const resCaptcha = await fetch(
        //   `https://www.google.com/recaptcha/api/siteverify?secret=${process.env.RECAPTCHA_PRIVATE_KEY}&response=${credentials.reCaptchaToken}`,
        //   {
        //     method: "POST",
        //   }
        // );
        // const responseCaptcha = await resCaptcha.json();
        // if (responseCaptcha?.success) {
        const loginModel = {
          client_id: process.env.IdentityServer4_CLIENT_ID,
          client_secret: process.env.IdentityServer4_CLIENT_SECRET,
          grant_type: "password",
          username: credentials.username,
          password: credentials.password,
          scope: process.env.IDENTITY_SCOPES,
        };

        const formBody = Object.keys(loginModel)
          .map(
            (key) =>
              encodeURIComponent(key) +
              "=" +
              encodeURIComponent(loginModel[key])
          )
          .join("&");

        const res = await fetch(
          `${process.env.IdentityServer4_Issuer}/connect/token`,
          {
            method: "POST",
            body: formBody,
            headers: {
              "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
            },
          }
        );
        var response = await res.json();
        if (res.ok && response) {
          return response;
        }
        // }
        return null;
      },
    }),
    CredentialsProvider({
      id: "is-refresh",
      name: "is-refresh",
      async authorize(credentials, req) {
        var session1 = await getSession({ req });
        const loginModel = {
          client_id: process.env.IdentityServer4_CLIENT_ID,
          client_secret: process.env.IdentityServer4_CLIENT_SECRET,
          grant_type: "refresh_token",
          refresh_token: session1.refreshToken,
        };

        const formBody = Object.keys(loginModel)
          .map(
            (key) =>
              encodeURIComponent(key) +
              "=" +
              encodeURIComponent(loginModel[key])
          )
          .join("&");

        const res = await fetch(
          `${process.env.IdentityServer4_Issuer}/connect/token`,
          {
            method: "POST",
            body: formBody,
            headers: {
              "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
            },
          }
        );
        var response = await res.json();
        if (res.ok && response) {
          return response;
        }
        return null;
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile, email, credentials }) {
      if (user?.access_token) {
        account.access_token = user?.access_token;
      }
      if (user?.refresh_token) {
        account.refresh_token = user?.refresh_token;
      }
      if (user) return true;
      else return false;
    },
    async redirect({ url, baseUrl }) {
      return baseUrl;
    },
    async session({ session, token, user }) {
      session.accessToken = token.access_token;
      session.idToken = token.idToken;
      session.user = token.user;
      session.refreshToken = token.refresh_token;
      session.expires = token.expires_at;
      return session;
    },
    async jwt({ token, trigger, account, profile, session }) {
      if (account?.access_token) {
        token.access_token = account?.access_token;
        var parsedToken = decode(account?.access_token);
        token.user = {
          name: parsedToken?.name,
          email: parsedToken?.email,
        };
        token.expires_at = parsedToken?.exp;
      }

      if (account?.refresh_token) {
        token.refresh_token = account?.refresh_token;
      }

      return token;
    },
  },
  pages: {
    signIn: "/login",
    signOut: "/logout",
  },
};

export default NextAuth(NextAuthOptions);
