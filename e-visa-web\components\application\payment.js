import {
  CalendarOutlined,
  CreditCardOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Button, Divider, Modal, Input, Tooltip, Checkbox } from "antd";
import React, { useEffect, useRef, useState } from "react";
import useStateRef from "react-usestateref";
import axioshelper, {
  BaseUrlType,
  MethodType,
} from "../../pages/api/axioshelper";
import showSweetAlertConfirm from "../../utils/sweetAlertConfirm";
import showSweetAlert from "../../utils/sweetAlert";
import { useDispatch, useSelector } from "react-redux";
import { setStep } from "../../store/reducer/stepperSlice";
import { clearApplication } from "../../store/reducer/applicaitonSlice";
import { useRouter } from "next/router";
import InputMask from "react-input-mask";
import { toast } from "react-toastify";
import { IconVisa, IconMastercard } from "../../utils/icons";
import useTranslation from "next-translate/useTranslation";
import { groupMin } from "../../pages/api/regulations";

const Payment = () => {
  React.useLayoutEffect = React.useEffect;
  const router = useRouter();
  const [holderName, setHolderName] = useState("");
  const [creditCardNumber, setCreditCardNumber] = useState("");
  const [expirationDate, setExpirationDate] = useState("");
  const [CVV, setCVV] = useState("");
  const [postValidation, setPostValidation] = useState(false);
  const [memberOrders, setMemberOrders, memberOrdersRef] = useStateRef([]);
  const [loadingAction, setLoadingAction] = useState(false);
  const [creditCardBrand, setCreditCardBrand] = useState("");
  const [isFront, setIsFront] = useState(true);
  const [selectedInput, setSelectedInput] = useState("");
  const [htmlContent, setHtmlContent, htmlContentRef] = useStateRef();
  const [detailOpen, setDetailOpen] = useState(false);
  const [isCreditCardValid, setIsCreditCardValid] = useState(true);
  const [isExpirationDateValid, setIsExpirationDateValid] = useState(true);
  const [isHolderNameValid, setIsHolderNameValid] = useState(true);
  const [isCVVValid, setIsCVVValid] = useState(true);
  const [isChecked, setChecked] = useState(false);
  const [showPdfModal, setShowPdfModal] = useState(false);
  const [pdfSrc, setPdfSrc] = useState("");
  const [binInfoFetched, setBinInfoFetched, binInfoFetchedRef] = useStateRef();
  const handleCancel = () => setDetailOpen(false);
  const { t, lang } = useTranslation();

  var holderNameRef = useRef("");
  var creditCardNumberRef = useRef("");
  var expirationDateRef = useRef("");
  var cvvRef = useRef("");

  const dispatch = useDispatch();
  var investigation = useSelector((state) => state.application.investigation);
  const currentStep = useSelector((state) => state.stepper.value);

  const onTextChanged = (ref, refName, e) => {
    if (refName == "holderName") {
      setHolderName(ref.current.input.value);
    } else if (refName == "creditCardNumber") {
      var value = e.target.value.replaceAll("_", "").replaceAll(" ", "");
      var cardNumberLength = value.length;

      if (cardNumberLength > 5) {
        if (!binInfoFetchedRef?.current) {
          setBinInfoFetched(true);
          axioshelper(
            MethodType.POST,
            BaseUrlType.Public_API,
            `payment/bin-info`,
            { BinNumber: value.substring(0, 6) }
          ).then((result) => {
            if (result?.status == "SUCCESS") {
              setCreditCardBrand(result?.data?.cardAssociation);
            }
          });
        }
      } else {
        setBinInfoFetched(false);
      }

      setCreditCardNumber(e.target.value);
    } else if (refName == "expirationDate") {
      setExpirationDate(e.target.value);
    } else if (refName == "CVV") {
      setCVV(e.target.value);
    }
  };

  const handleInputChange = (e) => {
    setSelectedInput(e.target.name);
    if (e.target.name == "cvc" && e.type == "focus") {
      setIsFront(false);
    } else {
      setIsFront(true);
    }
  };

  const getMemberOrders = async () => {
    var result = await axioshelper(
      MethodType.GET,
      BaseUrlType.Public_API,
      `investigations/${investigation.id}/member-orders`,
      null
    );
    if (result?.status == "SUCCESS") setMemberOrders(result.data);
  };
  useEffect(() => {
    if (currentStep == 4) {
      getMemberOrders();
    }
  }, [currentStep]);

  const removeMember = (id) => {
    //TODO
    var controlCount;
    var cancelInvestigation = false;
    if (investigation?.investigationTypeId == 1) controlCount = 1;
    else if (investigation?.investigationTypeId == 2) controlCount = 2;
    else if (investigation?.investigationTypeId == 3) controlCount = groupMin;

    var memberCount = memberOrdersRef?.current?.length;

    if (memberCount <= controlCount) {
      cancelInvestigation = true;
    }

    showSweetAlertConfirm(
      t("site:warning"),
      cancelInvestigation
        ? t("site:cancelInvestigationConfirm")
        : t("site:removeMemberConfirm"),
      "warning"
    ).then(async (result) => {
      if (result.isConfirmed) {
        var result = await axioshelper(
          MethodType.POST,
          BaseUrlType.Public_API,
          `investigations/${investigation.id}/members/${id}/cancel`,
          null
        );
        if (result?.status == "SUCCESS") {
          await getMemberOrders();

          router.push("/application/my-applications");
          setTimeout(() => {
            dispatch(clearApplication());
            dispatch(setStep(0));
          }, 2000);
        }
      }
    });
  };

  const pay = async () => {
    setLoadingAction(true);
    setPostValidation(true);
    var valid = true;
    if (CVV.replace("_", "").length != 3 || isCVVValid == false) {
      toast.warning(t("form:validateCard", { field: "CVV" }));
      setIsCVVValid(false);
      valid = false;
    }

    if (holderName.length < 4 || isHolderNameValid == false) {
      toast.warning(
        t("form:validateLength", { field: "Holder name", length: "3" })
      );
      setIsHolderNameValid(false);
      valid = false;
    }

    if (
      creditCardNumber.replace("_", "").length != 19 ||
      isCreditCardValid == false
    ) {
      toast.warning(t("form:validateCard", { field: "Card number" }));
      setIsCreditCardValid(false);
      valid = false;
    }

    if (
      expirationDate.replace("_", "").length != 5 ||
      isExpirationDateValid == false
    ) {
      toast.warning(t("form:validateCard", { field: "Expiration date" }));
      setIsExpirationDateValid(false);
      valid = false;
    }

    if (!isChecked) {
      toast.warning(t("form:agreementWarning"));
      valid = false;
    }

    var expireYear = expirationDate.split("/")[1];
    var expireMonth = expirationDate.split("/")[0];
    var items = [];

    for (
      let index = 0;
      index < memberOrdersRef?.current?.members.length;
      index++
    ) {
      var member = memberOrdersRef?.current?.members[index];
      for (let j = 0; j < member.prices.length; j++) {
        var order = member.prices[j];
        items.push({
          id: order.id.toString(),
          name: order.priceType.displayValue,
          category: "Dijital",
          price: order.price,
        });
      }
    }

    if (valid) {
      const baseUrl = process.env.NEXTAUTH_URL;
      const callBackUrl =
        lang === "tr"
          ? `${baseUrl}/payment/payment-result`
          : `${baseUrl}/${lang}/payment/payment-result`;

      // console.log("proice:", memberOrdersRef?.current?.totalTaxedFee);
      // console.log("items:", items);
      var result = await axioshelper(
        MethodType.POST,
        BaseUrlType.Public_API,
        `payment/init3ds`,
        {
          installment: 0,
          price: memberOrdersRef?.current?.totalTaxedFee,
          currency: "USD",
          card: {
            cardHolderName: holderName,
            cardNumber: creditCardNumber.replaceAll(" ", ""),
            expireYear: expireYear,
            expireMonth: expireMonth,
            cvv: CVV,
          },
          items: items,
          buyer: {
            id: 123,
            name: "Veli",
            surname: "Beyaz",
            email: "<EMAIL>",
            phone: "05551112233",
            address: "Kadıköy İSTANBUL",
            city: "İstanbul",
            country: "Türkiye",
          },
          ExternalTransactionId: investigation?.id.toString(),
          saleCategory: 1,
          securityRisk: 1,
          OkUrl: callBackUrl,
          FailUrl: callBackUrl,
        }
      );
      if (result?.status == "SUCCESS") {
        setHtmlContent(result?.data?.htmlContent);
        setDetailOpen(true);
      }
    }

    setLoadingAction(false);
  };

  useEffect(() => {
    const handleStorageChange = (event) => {
      if (event.key === "paymentStatus") {
        const paymentStatus = localStorage.getItem("paymentStatus");

        if (paymentStatus === "false") {
          localStorage.removeItem("paymentStatus");
          setDetailOpen(false);
          showSweetAlert(t("site:error"), t("site:paymentError"), "error");
        } else if (paymentStatus === "true") {
          localStorage.removeItem("paymentStatus");
          setDetailOpen(false);
          showSweetAlert(
            t("site:success"),
            t("site:paymentSuccess"),
            "success"
          ).then(() => {
            window.top.location.href =
              lang === "tr"
                ? "/application/my-applications/"
                : `/${lang}/application/my-applications/`;
          });
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  const validCreditCard = (e) => {
    setIsCreditCardValid(false);
    let value = e.target.value;
    if (value.length < 19) return false;
    if (/[^0-9-\s]+/.test(value)) return false;
    let nCheck = 0,
      bEven = false;
    value = value.replace(/\D/g, "");

    for (var n = value.length - 1; n >= 0; n--) {
      var cDigit = value.charAt(n),
        nDigit = parseInt(cDigit, 10);

      if (bEven && (nDigit *= 2) > 9) nDigit -= 9;

      nCheck += nDigit;
      bEven = !bEven;
    }

    if (nCheck % 10 == 0) {
      setIsCreditCardValid(true);
    }
  };

  const validExpirationDate = () => {
    setIsExpirationDateValid(false);
    if (expirationDate.replace("_", "").length != 5) return false;
    const currentDate = new Date();
    const currentMonth = String(currentDate.getMonth() + 1).padStart(2, "0");
    const currentYear = String(currentDate.getFullYear()).slice(-2);

    var expireYear = expirationDate.split("/")[1];
    var expireMonth = expirationDate.split("/")[0];
    if (currentYear == expireYear && currentMonth <= expireMonth) {
      setIsExpirationDateValid(true);
    } else if (currentYear < expireYear) {
      setIsExpirationDateValid(true);
    }
  };

  const validHolderName = () => {
    setIsHolderNameValid(holderName.length < 4 ? false : true);
  };

  const validCVV = () => {
    setIsCVVValid(CVV.replace("_", "").length != 3 ? false : true);
  };
  return (
    <>
      <Modal
        className="w-75"
        open={detailOpen}
        centered
        footer={null}
        onCancel={handleCancel}
        maskClosable={false}
        title={t("site:paymentProcess")}
      >
        {
          <iframe
            style={{ width: "100%", minHeight: 650 }}
            srcDoc={htmlContent}
          />
        }
      </Modal>

      <div className="row">
        <div className="col-12">
          <div className="card-group">
            <div className="row" style={{ flex: 1 }}>
              <div className="col-lg-8 col-md-8 col-sm-12">
                <div className="slf-payment-flex">
                  {/* kredi kartı */}
                  <div
                    className="flip-credit-card"
                    style={isFront ? {} : { transform: "rotateY(180deg)" }}
                  >
                    <div
                      className="flip-credit-card-inner"
                      style={isFront ? {} : { transform: "rotateY(180deg)" }}
                    >
                      <div className="text-white flip-credit-card-front px-2">
                        <div className="row px-2 pt-3">
                          <div className="col-6  text-start">
                            {t("site:creditCard")}
                          </div>
                          <div
                            className="col-6 text-end"
                            style={{ height: "55px" }}
                          >
                            {creditCardBrand == "VISA" && <IconVisa />}
                            {creditCardBrand == "MASTERCARD" && (
                              <IconMastercard />
                            )}
                          </div>
                        </div>
                        <div className="w-100"></div>
                        <div className="row py-4 px-2">
                          <div
                            className="col card-number fs-5"
                            style={
                              selectedInput == "number"
                                ? { fontWeight: "bold", height: "30px" }
                                : { height: "30px" }
                            }
                          >
                            {creditCardNumber}
                          </div>
                        </div>
                        <div className="w-100"></div>
                        <div className="row px-2 pt-2 text-start">
                          <div
                            className="col-6"
                            style={
                              selectedInput == "name"
                                ? { fontWeight: "bold" }
                                : {}
                            }
                          >
                            {holderName}
                          </div>
                          <div
                            className="col-6 text-end"
                            style={
                              selectedInput == "expiry"
                                ? { fontWeight: "bold" }
                                : {}
                            }
                          >
                            {expirationDate}
                          </div>
                        </div>
                        <div className="w-100"></div>
                      </div>
                      <div className="text-white  flip-credit-card-back">
                        <div className="credit-card-back-black-container"></div>
                        <div className="credit-card-cvv-area">
                          <div
                            className="col card-number fs-5 fw-bold"
                            style={{
                              transform: "rotateY(180deg)",
                              paddingLeft: "10rem",
                            }}
                          >
                            {CVV}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* isim ve kart numarası */}
                  <div className="slf-w-full">
                    <div className="row mt-4">
                      <div className="col-lg-6 col-md-6 col-sm-12 mb-2">
                        <label className="card-label" htmlFor="name">
                          {t("form:cardHolder")}
                        </label>
                        <Input
                          size="large"
                          placeholder={t("form:cardHolder")}
                          status={!isHolderNameValid ? "error" : ""}
                          onChange={(e) => {
                            onTextChanged(holderNameRef, "holderName");
                          }}
                          onFocus={handleInputChange}
                          onBlur={validHolderName}
                          id="name"
                          name="name"
                          onInput={(e) => {
                            const lettersOnly = e.target.value.replace(
                              /[^A-Za-zÇçĞğİıÖöŞşÜü ]/g,
                              ""
                            );
                            e.target.value = lettersOnly.toLocaleUpperCase(
                              router.locale
                            );
                          }}
                          ref={holderNameRef}
                          suffix={
                            <UserOutlined
                              style={{ color: "rgba(0,0,0,.45)" }}
                            />
                          }
                        />
                      </div>
                      <div className="col-lg-6 col-md-6 col-sm-12 mb-2">
                        <label className="card-label" htmlFor="number">
                          {t("form:cardNumber")}
                        </label>
                        <InputMask
                          mask="9999 9999 9999 9999"
                          value={creditCardNumber}
                          ref={creditCardNumberRef}
                          onChange={(e) => {
                            onTextChanged(
                              creditCardNumberRef,
                              "creditCardNumber",
                              e
                            );
                          }}
                          onFocus={handleInputChange}
                          onBlur={validCreditCard}
                          id="number"
                          name="number"
                        >
                          <Input
                            size="large"
                            value={creditCardNumber}
                            status={!isCreditCardValid ? "error" : ""}
                            placeholder="**** **** **** ****"
                            suffix={
                              <CreditCardOutlined
                                style={{ color: "rgba(0,0,0,.45)" }}
                              />
                            }
                          />
                        </InputMask>
                      </div>
                    </div>

                    {/* son kullanma tarihi ve cvv */}
                    <div className="row">
                      <div className="col-lg-6 col-md-6 col-sm-12 slf-payment-form-last-item">
                        <label className="card-label" htmlFor="expiry">
                          {t("form:expirationDate")}
                        </label>

                        <InputMask
                          mask="99/99"
                          value={expirationDate}
                          ref={expirationDateRef}
                          onChange={(e) => {
                            onTextChanged(
                              expirationDateRef,
                              "expirationDate",
                              e
                            );
                          }}
                          onFocus={handleInputChange}
                          onBlur={validExpirationDate}
                          id="expiry"
                          name="expiry"
                        >
                          <Input
                            size="large"
                            placeholder="**/**"
                            value={expirationDate}
                            status={!isExpirationDateValid ? "error" : ""}
                            ref={expirationDateRef}
                            suffix={
                              <CalendarOutlined
                                style={{ color: "rgba(0,0,0,.45)" }}
                              />
                            }
                          />
                        </InputMask>
                      </div>
                      <div className="col-lg-6 col-md-6 col-sm-12 slf-payment-form-last-item">
                        <label className="card-label" htmlFor="cvc">
                          {t("form:cvv")}
                        </label>
                        <InputMask
                          mask="999"
                          value={CVV}
                          ref={cvvRef}
                          onChange={(e) => {
                            onTextChanged(cvvRef, "CVV", e);
                          }}
                          onFocus={handleInputChange}
                          onBlur={(e) => {
                            handleInputChange(e);
                            validCVV();
                          }}
                          id="cvc"
                          name="cvc"
                        >
                          <Input
                            size="large"
                            placeholder="***"
                            value={CVV}
                            ref={cvvRef}
                            status={!isCVVValid ? "error" : ""}
                            suffix={
                              <Tooltip title={t("form:cvvTooltip")}>
                                <InfoCircleOutlined
                                  style={{ color: "rgba(0,0,0,.45)" }}
                                />
                              </Tooltip>
                            }
                          />
                        </InputMask>
                      </div>
                    </div>
                    <div className="row mt-5">
                      <div className="col d-flex align-items-center">
                        <Checkbox
                          className="ms-2"
                          checked={isChecked}
                          onChange={(e) => setChecked(e.target.checked)}
                        />
                        <div
                          className="ms-2"
                          dangerouslySetInnerHTML={{
                            __html: t("form:agreement", {
                              termsLink: t("form:termsLink"),
                              distanceSalesContract: t(
                                "form:distanceSalesContract"
                              ),
                            }),
                          }}
                        />
                      </div>
                    </div>
                    <div className="row mt-4 ms-3">
                      <div className="col">
                        <strong>
                          <a href="#">Sözleşme ve Formlar</a>
                        </strong>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-4 col-md-4 col-sm-12">
                <div className="slf-mobile-mt-3">
                  <h6>{t("site:paymentSummary")}</h6>
                  <div className="mb-auto p-2">
                    <div className="row py-1">
                      <div className="col">{t("site:visaFee")}</div>
                      <div className="col text-end fw-bold">
                        ${memberOrdersRef?.current?.totalVisaFee}
                      </div>
                    </div>
                    <div className="row py-1">
                      <div className="col">{t("site:serviceFee")}</div>
                      <div className="col text-end fw-bold">
                        ${memberOrdersRef?.current?.totalServiceFee}
                      </div>
                    </div>
                    <div className="row py-1">
                      <div className="col">{t("site:taxFee")}</div>
                      <div className="col text-end fw-bold">
                        $
                        {Number(
                          memberOrdersRef?.current?.totalTaxedFee -
                            memberOrdersRef?.current?.totalFee
                        ).toFixed(2)}
                      </div>
                    </div>
                    <div className="row py-1">
                      <div className="col">{t("site:total")}</div>
                      <div className="col text-end fw-bold">
                        ${memberOrdersRef?.current?.totalTaxedFee}
                      </div>
                    </div>
                  </div>
                  <div className="row mt-4">
                    <div className="col-12">
                      <Button
                        size="large"
                        className="stepper-button-next-block"
                        onClick={() => {
                          pay();
                        }}
                        style={{ width: "100%" }}
                        loading={loadingAction}
                      >
                        {t("form:payNow")}
                      </Button>
                    </div>
                  </div>
                  <Divider
                    className="text-purple slf-mt-30"
                    style={{ border: "1px solid rgb(236, 235, 250)" }}
                  ></Divider>
                  <h6> {t("site:paymentDetail")}</h6>
                  <div className="slf-payment-detail-wrap">
                    {memberOrdersRef?.current?.members?.map((order) => (
                      <div key={order.id}>
                        <div className="row col">
                          <div className="d-flex" key={order.id}>
                            <div
                              className="p-2 me-auto fw-bold"
                              style={{ width: "inherit" }}
                            >
                              {order?.name} {order?.surname}
                            </div>
                          </div>
                        </div>
                        <div className="row col">
                          <div className="d-flex" key={order.id}>
                            <div className="p-2 text-muted me-auto">
                              {t("site:visaFee")}:
                              {"$" +
                                order?.prices?.find((f) => f.priceType.id == 2)
                                  ?.price}
                            </div>
                            <div className="p-2">
                              <Tooltip title={t("site:delete")}>
                                <DeleteOutlined
                                  onClick={() => {
                                    removeMember(order.id);
                                  }}
                                  style={{ color: "red" }}
                                />
                              </Tooltip>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Payment;
