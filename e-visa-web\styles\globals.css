@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap");

* {
  box-sizing: border-box;
  font-family: "Poppins", sans-serif !important;
}

html {
  scroll-behavior: smooth !important;
}

html,
body {
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none !important;
}

img {
  max-width: 100%;
  height: auto;
}

h1,
h2,
p,
ul {
  margin: 0;
}

ul {
  padding: 0;
  list-style: none;
}

button {
  padding: 0.5rem 1rem;
  font-weight: bold;
}

.ant-menu-item {
  color: #4c1d95;
  font-size: 15px;
  padding-top: 12px;
  padding-bottom: 12px;
}

.ant-menu-submenu {
  color: #4c1d95;
}

.ant-menu-item-icon {
  font-size: 20px !important;
}

.ant-layout-sider {
  min-width: 250px !important;
  max-width: 250px !important;
}

.ant-layout-sider-collapsed {
  min-width: 80px !important;
  max-width: 80px !important;
}

.stepper-button-next {
  padding: 16px;
  background-color: #4c1d95 !important;
  color: white !important;
  text-align: center;
  align-content: space-around;
  height: 50px !important;
  width: 150px !important;
}

.stepper-button-next-block {
  padding: 16px;
  background-color: #4c1d95 !important;
  color: white !important;
  text-align: center;
  align-content: space-around;
  width: 100% !important;
}

.stepper-button-next-block:disabled {
  background-color: #4d1d95a1 !important;
}

.stepper-button-back {
  padding: 16px;
  background-color: #e4e2e7 !important;
  color: black !important;
  text-align: center;
  align-content: space-around;
  height: 50px !important;
  width: 150px !important;
  /* border: 1px solid #4C1D95 !important; */
}

.stepper-button-back:hover {
  border: 1px solid #4c1d95 !important;
}

.add-member-button {
  padding: 16px;
  color: #563f79 !important;
  background-color: white !important;
  text-align: center;
  align-content: space-around;
  height: 50px !important;
  width: 150px !important;
  font-weight: bold !important;
}

.search-history-button {
  color: white !important;
  text-align: center;
  align-content: space-around;
  background-color: #4c1d95 !important;
  font-weight: bold !important;
}

.ant-checkbox-group {
  display: flex;
  flex-direction: column;
}

.ant-form-item {
  margin-bottom: 2px !important;
}

.ant-upload-picture-card-wrapper {
  width: auto !important;
}

.flip-credit-card {
  background-color: transparent;
  width: 350px;
  height: 220px;
  perspective: 1000px;
  scale: 0.7;
}

.flip-credit-card-inner {
  position: relative;
  width: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
}

.flip-credit-card-front,
.flip-credit-card-back {
  position: absolute;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.flip-credit-card-front {
  width: 350px;
  height: 220px;
  border-radius: 12px;
  background: linear-gradient(
    68.44deg,
    #780c67 0%,
    #940967 41.98%,
    #b10667 73.75%,
    #d90266 100%
  );
  border-radius: 12px;
}

.flip-credit-card-back {
  width: 350px;
  height: 220px;
  border-radius: 12px;
  background: linear-gradient(
    68.44deg,
    #780c67 0%,
    #940967 41.98%,
    #b10667 73.75%,
    #d90266 100%
  );
  border-radius: 12px;
  transform: rotateY(180deg);
}

.flip-credit-card-front .card-number {
  word-spacing: 10px;
}

.credit-card-back-black-container {
  background-color: #000;
  padding: 0 !important;
  height: 3rem;
  margin-top: 1.5rem;
}

.credit-card-cvv-area {
  background-color: #acaebe;
  padding: 0 !important;
  height: 2rem;
  margin-top: 3rem;
}

.credit-card-text-uppercase {
  text-transform: uppercase;
}

.card-label {
  height: 40px;
  padding: 8px 0 8px 0;
}

.payment-box {
  height: 80px;
  width: 80px;
  background-color: #4c1d95;
  color: white;
  font-size: small;
  text-overflow: ellipsis;
  border-radius: 5px;
  padding: 10px;
}

.passport-card {
  border-radius: 5px;
  color: white;
  font-size: medium;
  background-color: #4d1d95;
}

.passport-card div {
  color: white;
}

.container {
  max-width: 950px !important;
  padding: 12px !important;
}

.call-center-item span {
  font-size: 1.3em;
  color: #4c1d95;
}

.call-center-item label {
  font-size: 1em;
}

.call-center-card {
  border: 1px solid #4c1d95 !important;
}

/**custom scroll bar**/
/* width */
::-webkit-scrollbar {
  width: 2px;
  height: 2px;
  padding-left: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  background: transparent;
  padding-left: 5px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #4c1d95;
  background-clip: padding-box;
  border-radius: 4px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.summaryCss {
  font-size: large;
  background-color: #ece6f4;
}

.ant-layout-header {
  line-height: 64px;
}

.ant-layout-header a {
  line-height: 30px;
}

.center-div {
  text-align: center;
  font-weight: bold;
}

.logo-title {
  font-size: 24px;
  font-weight: 700;
}

.logo-subtitle {
  font-size: 12px;
  letter-spacing: 6px;
  line-height: 10px;
  color: white;
}

.ant-steps-item-content {
  margin-top: 1px !important;
}

.ant-steps
  .ant-steps-item-finish
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title {
  color: #4c1d95 !important;
}

.ant-steps
  .ant-steps-item-process
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title {
  color: #4c1d95 !important;
}

.ant-steps-item-title {
  font-size: 13px !important;
}

.ant-steps
  .ant-steps-item-finish.ant-steps-item-custom
  .ant-steps-item-icon
  > .ant-steps-icon {
  color: #4c1d95 !important;
}

.ant-steps
  .ant-steps-item-process.ant-steps-item-custom
  .ant-steps-item-icon
  > .ant-steps-icon {
  color: #4c1d95 !important;
}

.ant-steps
  .ant-steps-item-finish
  > .ant-steps-item-container
  > .ant-steps-item-tail::after {
  background-color: #4c1d95 !important;
}

.ant-steps .ant-steps-item-title {
  line-height: 20px !important;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #4c1d95 !important;
  border-color: #4c1d95 !important;
}

.nav-purple {
  background-color: #4c1d95;
  color: white;
}

.nav-purple .container .responsive-navbar-nav {
  color: white;
}

.containerv3 {
  overflow: hidden !important;
  background: rgba(247, 247, 247, 0.5) !important;
  backdrop-filter: blur(50px) !important;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 10px 2px !important;
  transition: all 0.3s ease 0s !important;
}

body {
  /* background: #F1F2F4 */
  /*rgb(236, 235, 250)*/
  /* !important; */
  transition: all 0.3s 0s !important;
  min-height: 100vh !important;
  overflow-x: hidden;
}

.card-item {
  background-color: #fafaff !important;
}

.text-purple {
  color: #4c1d95 !important;
}

.icon-purple {
  color: #4d1d95c6 !important;
}

.preview-title {
  font-size: 14px;
}

.preview-subtitle {
  font-size: 13px;
}

.ant-btn > span {
  display: inline-flex !important;
}

.ant-divider {
  color: #4c1d95 !important;
}

.custom-footer {
  background-color: #c8cbd05e;
  height: 50px;
  width: 100%;
  position: relative;
  bottom: 0;
  padding-top: 10px;
  margin-top: 10px;
}

@media only screen and (max-height: 600px) {
  main {
    min-height: 600px;
  }
}

@media only screen and (max-height: 800px) {
  main {
    min-height: 800px;
  }
}

@media only screen and (max-height: 1200px) {
  main {
    min-height: calc(100vh - 140px);
    /*600px*/
  }
}

.ant-table-wrapper .ant-table-thead > tr > th {
  background-color: rgb(241, 245, 247) !important;
}

.bg-purple {
  background-color: #4c1d95 !important;
  color: white !important;
}

.ant-ribbon.ant-ribbon-placement-end {
  height: 35px !important;
  padding-right: 20px !important;
  padding-left: 20px !important;
  display: flex;
  align-items: center;
}

.footer-text {
  color: rgb(75, 75, 75);
}

.footer-text:hover {
  color: black;
}

.ant-upload-list-item-actions a {
  pointer-events: all !important;
  opacity: 1 !important;
}

.ant-upload-list-picture-circle .ant-upload-list-item {
  height: 250px !important;
  width: 250px !important;
}

.ant-upload-list-picture-circle .ant-upload {
  height: 250px !important;
  width: 250px !important;
}

.update-profile {
  padding: 16px;
  background-color: #4c1d95 !important;
  color: white !important;
  text-align: center;
  align-content: space-around;
}

.ant-upload-list-item-container,
.ant-upload.ant-upload-select {
  margin-bottom: 0 !important;
}

/* start:Tasarıma uygun css kodlarının bulunduğu alan */
/* Ana css kodları(diğer ekran boyutlarında ezilmediği sürece) ve dolayısıyla mobil ekranlar için olan kodlar */
.mr-14px {
  margin-right: 14px;
}

.nav-purple-gradient {
  background: linear-gradient(90deg, #46258c 0%, #7547d6 100%);
  padding: 0 !important;
  justify-content: center !important;
}

.slf-container {
  padding: 12px !important;
  max-width: 950px !important;
}

.slf-logo {
  display: flex;
  flex-direction: column;
}

.slf-logo .slf-t1 {
  color: #fff;
  font-size: 24px;
  font-weight: 700;
}

.slf-logo .slf-t2 {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 3.36px;
  margin-top: -12px;
}

.slf-lang-switcher {
  border-radius: 4px;
  width: 56px;
  height: 37px;
  z-index: 1;
  margin-top: 12px;
}

.slf-lang-switcher a {
  margin-top: -1px;
  height: 37px;
  padding: 8px;
}

.slf-lang-switcher .slf-lang-switcher-img {
  margin-top: -2px;
}

.slf-lang-switcher a::after {
  margin-left: 7px;
}

.slf-dropdown-item {
  font-size: 14px;
  height: 52px !important;
  padding: 0 !important;
}

.slf-dropdown-item:hover,
.slf-dropdown-item:active {
  background-color: #5b34ad !important;
  color: #fff !important;
}

.slf-dropdown-item-button {
  color: #252b37;
  font-size: 14px;
  font-weight: 500;
  line-height: 16px;
  width: 100%;
  text-align: left;
  height: 52px;
}

.slf-dropdown-item-button span {
  line-height: 23px;
}

.slf-login-button {
  border-radius: 4px;
  background: #fff !important;
  color: #252b37 !important;
  font-size: 14px;
  line-height: 21px;
  font-weight: 400;
  padding: 8px 12px !important;
  margin-right: 48px;
  width: fit-content;
  margin-top: 12px;
}

.slf-register-button {
  border-radius: 4px;
  border: 1px solid #fff !important;
  color: #fff !important;
  font-size: 14px;
  line-height: 21px;
  font-weight: 400;
  padding: 8px 12px !important;
  margin-right: 16px;
  width: fit-content;
  margin-top: 12px;
}

.slf-dropdown-item-button:hover {
  color: #fff !important;
}

.slf-profile-menu {
  margin-right: 48px;
  padding: 0 !important;
  /* margin-top: 12px; */
}

.slf-profile-menu-toggle {
  color: #fff !important;
  font-size: 14px;
  height: 40px;
  font-weight: 700 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.slf-profile-menu-toggle span {
  margin-left: 4px;
}

.slf-profile-menu-toggle::after {
  margin-left: 16px !important;
  margin-right: 4px;
}

.slf-header-links {
  margin-right: 40px;
}

.slf-header-link {
  color: #fff !important;
  font-size: 16px;
  font-weight: 400;
}

.slf-header-link .nav-link {
  color: #fff !important;
}

.slf-section-1 .slf-container {
  height: 900px;
  position: relative;
}

.slf-section-1 {
  background: url(/img/landing/section-1-2.png),
    url(/img/landing/section-1-1.jpg);
  background-repeat: no-repeat;
  height: 900px;
  background-size: cover, cover;
  background-position: top;
}

.slf-h-600 {
  height: 600px;
}

.slf-section-1-3 {
  display: none;
}

.slf-section-1-h1 {
  color: #fff;
  letter-spacing: -0.5px;
  margin: 0;
  margin-top: 24px;
  text-align: center;
}

.slf-section-1-h2 {
  color: #fff;
  opacity: 0.8;
  margin: 0;
  margin-top: 24px;
  text-align: center;
  font-weight: 400;
  font-size: 14px;
}

.slf-section-1-form {
  margin-top: 26px;
}

.slf-section-1-form-row {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.slf-section-1-form-row .slf-section-1-form-label {
  flex: auto;
}

.slf-section-1-form-label {
  margin-bottom: 20px !important;
}

.slf-section-1-form-label .ant-form-item-label {
  padding-bottom: 4px;
}

.slf-section-1-form-label label {
  color: #fff !important;
  font-size: 14px !important;
  font-weight: 700;
}

.slf-section-1-form-input {
  height: 60px;
}

.slf-section-1-form-buttons {
  margin-top: 10px;
}

.slf-section-1-action-button {
  border-radius: 8px;
  background: #7648d7;
  display: inline-flex;
  height: 60px;
  padding: 13px 33px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  color: #fff;
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
}

.slf-section-1-action-button:hover {
  background: #693fc5 !important;
}

.slf-section-1-h3 {
  color: #fff;
  font-size: 29px;
  font-weight: 400;
  line-height: 24px;
  margin-top: 70px;
  height: 68px;
}

.slf-section-1-text-column {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.slf-w-fit {
  width: fit-content;
}

.slf-section-2-for-order {
  display: flex;
  flex-direction: column;
}

.slf-section-2 {
  background: url(/img/landing/section-2-1.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: top;
  background-color: #fff;
}

.slf-section-2 .slf-container {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.slf-section-2-1-h2 {
  margin-top: 100px;
  margin-bottom: 0;
  color: #000b33;
  text-align: center;
  font-weight: 700;
  line-height: 57.6px;
  letter-spacing: -0.5px;
}

.slf-section-2-1-h3 {
  margin-top: 20px;
  margin-bottom: 0;
  color: #000b33;
  text-align: center;
  font-weight: 400;
  line-height: 26px;
  font-size: 16px;
}

.slf-section-2-1-rows {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 24px;
  margin-top: 50px;
  text-align: center;
}

.slf-section-2-1-row {
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 4px 40px 0px rgba(43, 89, 255, 0.08);
  flex: 1;
  min-height: 388px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.slf-section-2-1-row-image-1 {
  position: absolute;
  top: 31px;
}

.slf-section-2-1-row-image-2 {
  position: absolute;
  top: 53px;
}

.top-49px {
  top: 49px;
}

.top-47px {
  top: 47px;
}

.slf-section-2-1-row-h4 {
  margin-top: 134px;
  margin-bottom: 0;
  color: #000b33;
  font-size: 20px;
  font-weight: 700;
  line-height: 26px;
}

.slf-section-2-1-row-p {
  margin: 15px 55px 31px 55px;
  color: #000b33;
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  opacity: 0.7;
}

.slf-section-2-2-fix-height {
  height: 867px;
}

.slf-section-2-2 {
  margin-top: 177px;
  height: 690px;
  position: absolute;
  right: 0;
  left: 0;
  background: url(/img/landing/section-2-2-1.png);
  background-repeat: no-repeat;
  background-position: top;
  background-color: #2b59ff;
  opacity: 0.93;
}

.slf-section-2-2 .slf-container {
  padding: 0 !important;
  position: relative;
  height: 690px;
}

.slf-section-2-2-2 {
  display: none;
  margin-top: 15px;
  margin-left: 81px;
}

.slf-section-2-2-3 {
  display: none;
  position: absolute;
  top: -77px;
}

.slf-section-2-2-right {
  position: absolute;
  top: 138px;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  padding: 0 12px;
}

.slf-section-2-2-right-h2 {
  color: #fff;
  font-weight: 700;
  line-height: 57.6px;
  letter-spacing: -0.5px;
  margin-bottom: 0;
}

.slf-section-2-2-right-h3 {
  margin-top: 27px;
  margin-bottom: 0;
  color: #fff;
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  opacity: 0.8;
}

.slf-section-2-2-right-images {
  display: flex;
  flex-direction: row;
  gap: 18px;
  margin-top: 68px;
  justify-content: center;
  flex-wrap: wrap;
}

.slf-mt-177px {
  margin-top: 177px;
}

.slf-section-2-3-row-h4 {
  color: #000b33;
  font-size: 20px;
  font-weight: 600;
  line-height: 26px;
  text-align: left;
  width: 100%;
  padding: 15px 20px 0 20px;
  margin-bottom: 0;
}

.slf-section-2-3-row-subtitle {
  text-align: left;
  width: 100%;
  padding: 9px 20px 0 20px;
}

.slf-section-2-3-row-subtitle span {
  vertical-align: middle;
  margin-left: 8px;
  color: #536476;
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
  opacity: 0.8;
}

.slf-section-2-3-row-desc {
  text-align: left;
  width: 100%;
  color: #000b33;
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  opacity: 0.8;
  padding: 7px 20px 0 20px;
  margin-bottom: 0;
  flex: 1;
  min-width: 280px;
}

.slf-section-2-3-row-link {
  text-align: left;
  width: 100%;
  padding: 20px;
  margin-bottom: 0;
  color: #fd4c5c !important;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}

.ml-8px {
  margin-left: 8px;
}

.ml-10px {
  margin-left: 10px;
}

.slf-display-none {
  display: none;
}

.slf-section-2-4-fix-height {
  height: 840px;
}

.slf-section-2-4 {
  margin-top: 100px;
  height: 740px;
  position: absolute;
  right: 0;
  left: 0;
  background: url(/img/landing/section-2-4-1.png);
  background-repeat: no-repeat;
  background-size: 100% 740px;
  background-position: top;
}

.slf-section-2-4 .slf-container {
  padding: 0 12px !important;
  position: relative;
  height: 740px;
}

.mt-77px {
  margin-top: 77px;
}

.slf-section-2-4-row {
  display: flex;
  flex-direction: column;
  height: 100%;
  align-items: center;
  justify-content: center;
  gap: 12px;
  text-align: center;
}

.slf-section-2-4-row-h2 {
  color: #000b33;
  font-weight: 700;
  line-height: 57.6px;
  letter-spacing: -0.5px;
  margin-bottom: 0;
}

.slf-section-2-4-row-p {
  margin-top: 19px;
  margin-bottom: 0;
  color: #000b33;
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  opacity: 0.7;
}

.slf-section-2-4-row-action-button {
  margin-top: 16px;
  border-radius: 8px;
  background: #fd4c5c;
  display: inline-flex;
  padding: 13px 33px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  color: #fff;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  height: 50px;
}

.slf-section-2-4-row-action-button:hover {
  background: #eb404e !important;
}

.slf-section-3 {
  background-color: #fff;
}

.slf-section-3 .slf-container {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  text-align: center;
}

.slf-section-3-h2 {
  padding-top: 100px;
  margin-bottom: 0;
  color: #000b33;
  font-weight: 700;
  line-height: 57.6px;
  letter-spacing: -0.5px;
}

.slf-section-3-flex {
  display: flex;
  flex-direction: row;
  gap: 40px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 24px;
}

.slf-section-3-button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 8px;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  height: 50px;
  background: #fff;
  border: 2px solid #502c9c;
  color: #502c9c;
}

.slf-section-3-button:hover {
  background: #3e1c87 !important;
}

.slf-section-3-button.active {
  background: #502c9c;
  color: #fff;
}

.slf-section-3-accordion {
  margin-top: 40px;
  padding-bottom: 100px;
}

.slf-section-3-accordion .accordion-item {
  border-radius: 0 !important;
  border-right: 0;
  border-left: 0;
}

.slf-section-3-accordion .accordion-item .accordion-header .accordion-button {
  background: #fff;
  border-color: #fff;
  border-radius: 0;
  box-shadow: none;
  color: #000b33;
  font-size: 20px;
  font-weight: 600;
  line-height: 26px;
  padding-top: 30px;
  padding-bottom: 30px;
}

.slf-section-3-accordion
  .accordion-item
  .accordion-header
  .accordion-button::after {
  background: url(/img/landing/plus.jpg);
  width: 16px;
  height: 16px;
  background-repeat: no-repeat;
}

.slf-section-3-accordion
  .accordion-item
  .accordion-header
  .accordion-button:not(.collapsed)::after {
  background: url(/img/landing/minus.jpg);
  width: 16px;
  height: 16px;
  background-repeat: no-repeat;
  background-position-y: center;
}

.slf-section-3-accordion .accordion-item .accordion-collapse .accordion-body {
  text-align: left;
  padding-top: 0;
  margin-top: -5px;
  color: #000b33;
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  opacity: 0.8;
}

.slf-footer {
  background: #fff;
  padding-bottom: 40px;
}

.slf-footer .slf-container {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.slf-footer-flex {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
}

.slf-footer-flex-columns {
  flex: 1 auto;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.slf-footer-flex-columns h3 {
  color: #000b33;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  margin-bottom: 2px;
}

.slf-footer-flex-columns a {
  color: #000b33;
  font-size: 14px;
  font-weight: 400;
  line-height: 26px;
  opacity: 0.7;
}

.slf-footer-flex-columns a:hover {
  color: #7648d7;
}

.slf-footer-flex-columns b {
  opacity: 0.7;
}

.slf-footer-flex-columns p {
  margin-bottom: 0;
}

.slf-w-417px {
  width: 417px;
}

.slf-footer-flex-rows {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-around;
  gap: 12px;
}

.slf-footer-flex-rows-columns {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 12px;
}

.slf-footer-flex-rows-columns-rows {
  display: flex;
  flex-direction: row;
  gap: 12px;
}

.slf-footer-flex-rows-columns-rows-link {
  color: #000 !important;
  font-size: 25px !important;
  font-weight: 700 !important;
  line-height: 24px !important;
}

.slf-footer-flex-rows-columns-rows-p {
  color: #000;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  margin-top: -3px;
}

.slf-footer-kvkk {
  margin-top: 50px;
  padding-top: 22px;
  padding-bottom: 22px;
  border-top: 1px solid rgba(0, 11, 51, 0.1);
  border-bottom: 1px solid rgba(0, 11, 51, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-around;
  gap: 12px;
  flex-wrap: wrap;
}

.slf-footer-kvkk span {
  margin-left: 13px;
  color: #536476;
  font-size: 15px;
  font-weight: 400;
  line-height: 26px;
}

.slf-footer-kvkk span a {
  color: #7547d7 !important;
  text-decoration-line: underline !important;
}

.slf-footer-tc {
  margin-top: 28px;
  text-align: center;
  color: #000b33;
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  opacity: 0.9;
}

.slf-preview-width {
  width: calc(100% - 38px);
}

.slf-footer-flex-columns-link-inherit {
  color: inherit !important;
  font-size: inherit !important;
  line-height: inherit !important;
  opacity: inherit !important;
}

.slf-lang-switcher .dropdown-menu {
  margin-top: 13px !important;
  border-radius: 0px 0px 8px 8px;
  box-shadow: 0px 4px 2px 0px rgba(0, 0, 0, 0.25);
  padding: 0;
  border: none;
}

.slf-lang-switcher .dropdown-menu .slf-dropdown-item:last-of-type {
  border-radius: 0px 0px 8px 8px;
}

.slf-recaptcha {
  transform: scale(0.78);
  transform-origin: 0 0;
  width: 240px !important;
  margin-top: 36px;
}

.slf-payment-flex {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: center;
  place-content: center;
}

.slf-mt-30 {
  margin-top: 30px;
}

.slf-mt-12 {
  margin-top: 12px;
}

.slf-mb-11 {
  margin-bottom: 11px;
}

.slf-mb-8 {
  margin-bottom: 8px;
}

.slf-payment-detail-wrap {
  overflow-y: scroll;
  overflow-x: hidden;
  max-height: 308px;
}

.slf-member-files-wrap {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.slf-member-files {
  flex: 1 1 500px;
}

.slf-w-full {
  width: 100%;
}

.slf-payment-form-last-item {
  margin-bottom: 0.5rem;
}

.slf-mobile-mt-3 {
  margin-top: 1rem;
}

.slf-searchbar-flex-wrap {
  gap: 16px;
  justify-content: flex-end;
  display: flex;
  flex-wrap: wrap;
}

.slf-searchbar-flex {
  flex: 1 1 140px;
}

.slf-w-full-center {
  width: 100% !important;
  text-align: center;
}

.slf-mt-48-responsive {
  margin-top: 24px;
}

.slf-mt-60-responsive {
  margin-top: 36px;
}

.slf-mt-72-responsive {
  margin-top: 48px;
}

.slf-btn-visas-individual,
.slf-btn-visas-family,
.slf-btn-visas-group {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 10px;
  text-align: center;
  font-size: 20px;
  font-weight: 700;
  line-height: 26px;
  height: 124px;
  width: 100%;
  background: #e2e2e22d;
  border: 2px solid #e2e2e2c0;
  color: #000b33;
  padding: 16px 8px;
  box-shadow: 0px 4px 40px 0px rgba(43, 89, 255, 0.08);
}

.slf-btn-visas-individual:hover {
  background: #52c41a2d !important;
  color: #000b33 !important;
  border: 2px solid #52c41a;
}

.slf-btn-visas-family:hover {
  background: #1677ff2d !important;
  color: #000b33 !important;
  border: 2px solid #1677ff;
}

.slf-btn-visas-group:hover {
  background: #fa541c2d !important;
  color: #000b33 !important;
  border: 2px solid #fa541c;
}

.slf-btn-visas-desc {
  color: #000b33;
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  text-wrap: wrap;
}

.slf-w-full-responsive {
  width: 100% !important;
}

.slf-personal-info-flex-wrap {
  display: flex;
  flex-direction: row;
  gap: 16px;
  flex-wrap: wrap;
}

.slf-personal-info-flex {
  flex: 1 1 200px;
}

/* Tablet ekranlar için */
@media (min-width: 768px) {
  .slf-container {
    min-width: 768px !important;
  }

  .slf-section-2-2 {
    background-size: 100%;
  }

  .slf-section-2-2-right {
    width: 416px;
    padding: 0;
  }

  .slf-section-2-2-right-h2 {
    font-size: 48px;
  }

  .slf-section-2-4-row-h2 {
    font-size: 48px;
    width: 446px;
  }

  .slf-section-2-4-row-p {
    width: 446px;
  }

  .slf-section-3-h2 {
    font-size: 48px;
  }

  .slf-section-3-flex {
    margin-top: 50px;
  }

  .slf-section-3-button {
    padding: 13px 33px;
  }

  .slf-footer-flex-rows {
    justify-content: space-between;
  }

  .slf-footer-kvkk {
    justify-content: space-between;
  }

  ::-webkit-scrollbar {
    width: 4px;
    height: 5px;
  }

  .slf-recaptcha {
    transform: inherit;
    transform-origin: inherit;
    width: inherit;
  }

  .slf-payment-form-last-item {
    margin-bottom: 0;
  }

  .slf-mobile-mt-3 {
    margin-top: 0;
  }

  .slf-searchbar-flex {
    flex: 0 1 140px;
  }

  .slf-mt-48-responsive {
    margin-top: 48px;
  }

  .slf-mt-60-responsive {
    margin-top: 60px;
  }

  .slf-mt-72-responsive {
    margin-top: 72px;
  }
}

/* Kullanılan menü 992px ve altında mobil görünüme geçiyor onlar için */
@media (min-width: 992px) {
  .slf-container {
    min-width: 992px !important;
  }

  ::-webkit-scrollbar {
    height: 7px;
  }

  .slf-lang-switcher,
  .slf-login-button,
  .slf-register-button,
  .slf-profile-menu {
    margin-top: 0;
  }

  .slf-header-links {
    gap: 12px;
  }

  .slf-section-1 .slf-container {
    height: 900px;
  }

  .slf-section-1 {
    height: 900px;
    background-size: contain, 100% 900px;
    background-position: center;
    position: relative;
  }

  .slf-section-1-white-mask {
    width: 100%;
    background: #fff;
    height: 50px;
    transform: skewY(4deg);
    position: absolute;
    z-index: 1;
    bottom: 0;
  }

  .slf-section-1-3 {
    position: absolute;
    right: 0;
    bottom: 0;
    display: block;
  }

  .slf-section-1-h1 {
    font-size: 42px;
    font-weight: 600;
    margin-top: 89px;
    text-align: left;
  }

  .slf-section-1-h2 {
    text-align: left;
    font-weight: 600;
    font-size: 16px;
  }

  .slf-section-1-form {
    width: 400px;
  }

  .slf-section-1-h3 {
    margin-top: 35px;
  }

  .slf-section-1-text-column {
    align-items: start;
  }

  .slf-section-2-1-h2 {
    font-size: 48px;
  }

  .slf-section-2-2 {
    background-size: 992px;
  }

  .slf-section-2-2-2 {
    display: initial;
    margin-left: 61px;
  }

  .slf-section-2-2-3 {
    display: initial;
    left: 108px;
  }

  .slf-section-2-2-right {
    top: 138px;
    right: 12px;
    left: initial;
    margin-left: initial;
    margin-right: initial;
    text-align: initial;
  }

  .slf-section-2-4-row {
    flex-direction: row;
    justify-content: space-between;
    text-align: initial;
  }

  .slf-profile-menu .dropdown-menu {
    margin-top: 16px !important;
    min-width: 224px;
    border-radius: 0px 0px 8px 8px;
    box-shadow: 0px 4px 2px 0px rgba(0, 0, 0, 0.25);
    padding: 0;
    border: none;
  }

  .slf-profile-menu .dropdown-menu .slf-dropdown-item:last-of-type {
    border-radius: 0px 0px 8px 8px;
  }

  .slf-lang-switcher .dropdown-menu {
    margin-top: 19px !important;
  }

  .slf-w-full-responsive {
    width: 75% !important;
  }
}

/* Tam ekran desktop çözünürlüğü ve büyük ekranlar için */
@media (min-width: 1296px) {
  .slf-container {
    min-width: 1296px !important;
  }

  .slf-header-links {
    gap: 32px;
  }

  .slf-section-1-white-mask {
    transform: skewY(2deg);
  }

  .slf-section-1-h1 {
    font-size: 72px;
    font-weight: 700;
    line-height: 79.2px;
    width: 817px;
  }

  .slf-section-1-form {
    width: 636px;
    margin-top: 47px;
  }

  .slf-section-1-form-row {
    flex-direction: row;
    gap: 24px;
  }

  .slf-section-2-2 {
    background-size: 1272px;
  }

  .slf-section-2-2-2 {
    margin-left: 100px;
  }

  .slf-section-2-2-3 {
    left: 147px;
  }

  .slf-section-2-2-right {
    top: 138px;
    left: 649px;
    right: initial;
  }
}

/* end:Tasarıma uygun css kodlarının bulunduğu alan */

/* start: Login ekran css kodları */
.main {
  display: flex;
  flex-direction: row;
  height: 100vh;
  min-height: 793px;
}

.left {
  display: none;
  position: relative;
}

.left-bottom-mask {
  position: absolute;
  bottom: 0;
  background: linear-gradient(
    to top,
    rgba(255, 255, 255, 1) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  height: 330px;
  left: 0;
  right: 0;
}

.right {
  display: flex;
  flex-direction: column;
  flex: auto;
}

.right::before {
  background-image: url(/images/left-bg.png), url(/images/left-bg-merge.jpg);
  content: "";
  background-size: cover;
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  opacity: 0.1;
  z-index: -1;
}

.right-wrap {
  display: flex;
  flex-direction: column;
  flex: auto;
  margin: 0 40px;
}

.right-header {
  height: 105px;
}

.right-header-logo {
  float: left;
  margin-top: 40px;
}

.right-header-lang {
  float: right;
  position: relative;
  cursor: pointer;
  width: 56px;
  height: 33px;
  background: #f3eefd;
  border: 1px solid #f3eefd;
  border-radius: 4px;
  margin-top: 40px;
}

.right-header-lang-flag {
  position: absolute;
  top: 9px;
  left: 9px;
}

.right-header-lang-arrow {
  position: absolute;
  top: 11px;
  right: 4px;
}

.right-body {
  margin: auto 0;
  padding: 24px 0;
}

.right-body h1 {
  color: #252b37;
  font-weight: 700;
  font-size: 32px;
  line-height: 39px;
}

.right-body-welcome {
  margin-top: 10px;
  color: #86869b;
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;
}

.right-body form .form-group:first-child {
  margin-top: 40px;
}

.right-body form .form-group {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
}

.right-body form .form-group-2 .form-group:first-child {
  margin-top: 24px;
}

.right-body form .form-group-2 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 0 24px;
}

.right-body form .form-group label {
  font-weight: 700;
  font-size: 14px;
  line-height: 17px;
  color: #252b37;
  cursor: pointer;
}

.right-body form .form-group input {
  margin-top: 6px;
  background: #ffffff;
  border: 1px solid #dcdce4;
  border-radius: 8px;
  font-weight: 400;
  font-size: 16px;
  padding: 15px 10px;
}

.form-control:focus {
  border-color: #4096ff !important;
  box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1) !important;
  border-inline-end-width: 1px !important;
  outline: 0 !important;
}

.right-body form .form-control-password {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 0;
}

.right-body form .form-control-password input {
  margin-top: 0;
  border: none;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.right-body form .form-group .form-group-checkbox {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #86869b;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.right-body form .form-group .form-group-checkbox input {
  width: 16px;
  height: 16px;
  margin: 0 8px 0 0;
  padding: 0;
  vertical-align: bottom;
  cursor: pointer;
}

.right-body form .form-group .form-group-checkbox a {
  color: #46278e;
}

.right-body form .btn-submit {
  margin-top: 32px;
  height: 52px;
  width: 100%;
  background: linear-gradient(90deg, #46258c 0%, #7547d6 100%);
  border-radius: 8px;
  border: none;
  font-weight: 700;
  font-size: 14px;
  line-height: 17px;
  color: #ffffff;
  cursor: pointer;
}

.right-body form .btn-submit:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.right-body .right-body-reset-password {
  margin-top: 24px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  text-align: center;
  color: #7648d7;
  display: block;
}

.right-body hr {
  margin: 24px 0;
  height: 1px;
  border: none;
  background: #d9d9d9;
}

.right-body .right-body-register {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  text-align: center;
  color: #86869b;
}

.right-body .right-body-register .right-body-register-link {
  font-weight: 700;
  color: #7547d7;
}

.flex-auto {
  flex: auto;
}

.mt-24 {
  margin-top: 24px;
}

.left-group-tech-text {
  position: absolute;
  z-index: 3;
  left: 110px;
  top: 575px;
  color: #666;
  font-size: 12px;
  font-weight: 700;
}

@media only screen and (min-width: 476px) {
  .right-wrap {
    margin: 0 82px;
  }

  .right-header {
    height: 128px;
  }

  .right-header-logo {
    margin-top: 63px;
  }

  .right-header-lang {
    margin-top: 63px;
  }

  .flip-credit-card {
    scale: inherit;
  }
}

@media only screen and (min-width: 901px) {
  .left {
    display: flex;
    width: 50%;
    min-width: 530px;
    min-height: 793px;
    background-image: url(/images/left-bg.png), url(/images/left-bg-merge.jpg);
    background-size: 100% 100%;
  }

  .left-group {
    margin: auto;
    width: 530px;
    height: 793px;
    position: relative;
  }

  .left-group-58 {
    position: absolute;
    z-index: 1;
    right: 27px;
    top: -20px;
  }

  .left-group-62 {
    position: absolute;
    z-index: 1;
    right: 0;
    top: 397px;
  }

  .left-group-59 {
    position: absolute;
    z-index: 1;
    left: -20px;
    top: 197px;
  }

  .left-group-3 {
    position: absolute;
    z-index: 2;
    left: 149px;
    top: 93px;
  }

  .left-group-60 {
    position: absolute;
    z-index: 3;
    left: -17px;
    top: 530px;
  }

  .left-group-app-store {
    position: absolute;
    left: 60px;
    bottom: 0;
  }

  .left-group-google-play {
    position: absolute;
    right: 60px;
    bottom: 0;
  }

  .right {
    width: 50%;
  }

  .right-wrap {
    max-width: 520px;
    margin: 0 82px;
  }

  .right::before {
    display: none;
  }
}

@media only screen and (min-width: 901px) and (max-width: 1091px) {
  .right-wrap {
    margin: 0 40px;
  }
}

.btn-outline-purple {
  border: 1px solid #4c1d95 !important;
  border-radius: 5px;
  color: #4c1d95 !important;
  background-color: transparent !important;
}

.purple-shadow {
  box-shadow: 0px 0px 20px #4d1d9551;
}

/* end: Login ekran css kodları */
