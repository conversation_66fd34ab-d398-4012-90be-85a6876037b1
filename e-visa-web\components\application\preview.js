import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>se, Divider, Modal } from "antd";
import useTranslation from "next-translate/useTranslation";
import React, { useEffect, useState } from "react";
import axioshelper, {
  BaseUrlType,
  MethodType,
} from "../../pages/api/axioshelper";
import { useSelector, useDispatch } from "react-redux";
import { UserAddOutlined } from "@ant-design/icons";
import { setStep } from "../../store/reducer/stepperSlice";
import showSweetAlertConfirm from "../../utils/sweetAlertConfirm";
import showSweetAlert from "../../utils/sweetAlert";
import {
  setInvestigation,
  clearApplication,
} from "../../store/reducer/applicaitonSlice";
import {
  BsCalendarDate,
  BsCalendarMonth,
  BsFillFileEarmarkFill,
  BsFillFilePersonFill,
  BsFillPersonVcardFill,
} from "react-icons/bs";
import { BiShow } from "react-icons/bi";
import { GiPassport } from "react-icons/gi";
import { FaGlobeEurope } from "react-icons/fa";
import { MdFamilyRestroom } from "react-icons/md";
import { useRouter } from "next/router";
import dayjs from "dayjs";
import { getDateFormat } from "../../utils/tools";
import { toast } from "react-toastify";
import { groupMin, groupMax, familyMax } from "../../pages/api/regulations";

const Preview = () => {
  React.useLayoutEffect = React.useEffect;
  const { t } = useTranslation();
  const [members, setMembers] = useState([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [isImage, setIsImage] = useState(true);
  const [previewImage, setPreviewImage] = useState("");
  const [previewTitle, setPreviewTitle] = useState("");
  const [canProceed, setCanProceed] = useState(false);
  const [canAddMember, setCanAddMember] = useState(false);
  const [loadingAction, setLoadingAction] = useState(false);
  var router = useRouter();
  const { Panel } = Collapse;

  const handleCancel = () => setPreviewOpen(false);
  const dispatch = useDispatch();
  var investigation = useSelector((state) => state.application.investigation);
  const currentStep = useSelector((state) => state.stepper.value);

  useEffect(() => {
    const fetchData = async () => {
      setCanAddMember(false);

      var result = await axioshelper(
        MethodType.GET,
        BaseUrlType.Public_API,
        `investigations/${investigation.id}`,
        null
      );

      if (result?.status == "SUCCESS") {
        setMembers(result.data.investigationMembers);
      }
    };
    if (investigation?.id > 0 && currentStep == 3) {
      fetchData();
    }
  }, [investigation, router.locale, currentStep]);

  if (members.length > 0) {
    var currentMember = members.find(
      (e) => e.id == investigation.currentMemberId
    );
  }

  const showPreview = async (id, title) => {
    var index = investigation.members.findIndex((e, i) => {
      return e.investigationMemberId == investigation.currentMemberId;
    });
    var files = investigation.members[index].files;
    var selectedFile = files.find((f) => f.id == id);

    if (selectedFile?.file != null) {
      if (selectedFile?.file.url.includes("data:application/pdf")) {
        setIsImage(false);
      } else {
        setIsImage(true);
      }

      setPreviewTitle(title);
      setPreviewImage(selectedFile?.file.url);
      setPreviewOpen(true);
    }
  };

  const AddNewMember = async () => {
    showSweetAlertConfirm(
      t("site:warning"),
      t("site:newMemberConfirm"),
      "warning"
    ).then(async (result) => {
      if (result.isConfirmed) {
        var result = await axioshelper(
          MethodType.POST,
          BaseUrlType.Public_API,
          `investigations/${investigation.id}/members/${investigation.currentMemberId}/complete`,
          { isPaymentStep: false }
        );
        if (result?.status == "SUCCESS" || result?.status == 422) {
          dispatch(setStep(0));
          dispatch(
            setInvestigation({
              id: investigation.id,
              currentMemberId: 0,
              investigationTypeId: investigation?.investigationTypeId,
            })
          );
        }
      }
    });
  };

  const onChangeCheckBox = (e) => {
    var checked = e.target.checked;
    if (checked) {
      if (investigation.status?.id == 2) {
        // ödeme tamamlandı - revizyon
        setCanProceed(true);
        setCanAddMember(false);
      } else {
        var memberCount = investigation?.members.length;
        if (parseInt(investigation?.investigationTypeId) == 1) {
          //bireysel
          setCanAddMember(false);
          setCanProceed(true);
        } else if (parseInt(investigation?.investigationTypeId) == 2) {
          //aile
          setCanAddMember(memberCount <= familyMax);
          setCanProceed(memberCount > 1);
        } else if (parseInt(investigation?.investigationTypeId) == 3) {
          //grup
          setCanAddMember(memberCount <= groupMax);
          setCanProceed(memberCount >= groupMin);
        }
      }
    } else {
      setCanAddMember(false);
      setCanProceed(false);
    }
  };

  const movePaymentStep = async () => {
    setLoadingAction(true);
    if (investigation.status?.id == 2) {
      // ödeme tamamlandı
      var result = await axioshelper(
        MethodType.POST,
        BaseUrlType.Public_API,
        `investigations/${investigation.id}/members/${investigation.currentMemberId}/complete`,
        { isPaymentStep: false }
      );

      if (result?.status == "SUCCESS") {
        showSweetAlert(
          t("site:success"),
          t("site:successMessagev2"),
          "success"
        ).then(() => {
          router.push("/application/my-applications");
          setTimeout(() => {
            dispatch(clearApplication());
            dispatch(setStep(0));
          }, 1000);
        });
      }
    } else {
      var waitingToComplete = investigation.members.filter(
        (f) => f?.memberStatus?.id == 1
      );
      if (waitingToComplete?.length > 1) {
        var result = await axioshelper(
          MethodType.POST,
          BaseUrlType.Public_API,
          `investigations/${investigation.id}/members/${investigation.currentMemberId}/complete`,
          { isPaymentStep: false }
        );

        if (result?.status == "SUCCESS") {
          showSweetAlert(
            t("site:success"),
            t("site:waitingAppSuccessMessage"),
            "success"
          ).then(() => {
            router.push("/application/my-applications");
            setTimeout(() => {
              dispatch(clearApplication());
              dispatch(setStep(0));
            }, 1000);
          });
        } else {
          toast.error(t("site:waitingAppErrorMessage"));
        }
      } else {
        showSweetAlertConfirm(
          t("site:warning"),
          t("site:paymentStepConfirm"),
          "warning"
        ).then(async (result) => {
          setLoadingAction(true);
          if (result.isConfirmed) {
            var result = await axioshelper(
              MethodType.POST,
              BaseUrlType.Public_API,
              `investigations/${investigation.id}/members/${investigation.currentMemberId}/complete`,
              { isPaymentStep: true }
            );
            if (result?.status == "SUCCESS") {
              dispatch(setStep(4));
            }
          }
          setLoadingAction(false);
        });
      }
    }
    setLoadingAction(false);
  };

  return (
    <div key={investigation.currentMemberId}>
      <Modal
        className={isImage ? "" : "w-50"}
        open={previewOpen}
        title={previewTitle}
        footer={null}
        onCancel={handleCancel}
      >
        {isImage ? (
          <img
            alt="example"
            style={{
              width: "100%",
            }}
            src={previewImage}
          />
        ) : (
          <embed
            style={{ width: "100%", minHeight: 450 }}
            src={previewImage}
          ></embed>
        )}
      </Modal>

      <div className="">
        {canAddMember ? (
          <>
            <div className="row mb-4">
              <div className="col-6 px-5"></div>
              <div className="col-6 text-end">
                <Button
                  size="large"
                  type="text"
                  icon={<UserAddOutlined />}
                  onClick={() => {
                    AddNewMember();
                  }}
                  style={{ color: "#4C1D95", border: "1px solid" }}
                >
                  {t("site:addNewMember")}
                </Button>
              </div>
            </div>
          </>
        ) : (
          <></>
        )}
        {investigation?.members?.length > 1 ? (
          <Collapse className="slf-mt-12 mb-2">
            <Panel header={t("site:othersInApplication")} key="1">
              <table className="table table-striped">
                <thead>
                  <th scope="col">Name</th>
                  <th scope="col">Surname</th>
                  <th scope="col">PassportNumber</th>
                </thead>
                <tbody>
                  {investigation?.members
                    ?.filter(
                      (f) =>
                        f.investigationMemberId !=
                        investigation?.currentMemberId
                    )
                    .map((member) => (
                      <tr>
                        <td>{member.name}</td>
                        <td>{member.surname}</td>
                        <td>{member.passportNumber}</td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </Panel>
          </Collapse>
        ) : null}
        <div className="card">
          <div className="card-body">
            <div className="row">
              <div className="col-12 mt-2">
                <h6 className="text-purple fw-bold">
                  {t("form:passportInfo")}
                </h6>
                <div className="row">
                  <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                    <table>
                      <tbody>
                        <tr>
                          <td>
                            <GiPassport size={30} className="icon-purple" />{" "}
                          </td>
                          <td>
                            <label
                              className="text-muted preview-title fw-bold"
                              style={{ display: "block" }}
                            >
                              {t("form:passportType")}
                            </label>
                            <label className="preview-subtitle">
                              {currentMember?.passportType?.displayValue}
                            </label>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                    <table>
                      <tbody>
                        <tr>
                          <td>
                            <BsFillPersonVcardFill
                              size={30}
                              className="icon-purple me-2"
                            />{" "}
                          </td>
                          <td>
                            <label
                              className="text-muted fw-bold preview-title"
                              style={{ display: "block" }}
                            >
                              {t("form:visaType")}
                            </label>
                            <label className="preview-subtitle">
                              {currentMember?.visaType?.displayValue}
                            </label>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                    <table>
                      <tbody>
                        <tr>
                          <td>
                            <BsCalendarDate
                              size={30}
                              className="icon-purple me-2"
                            />{" "}
                          </td>
                          <td>
                            <label
                              className="text-muted fw-bold preview-title"
                              style={{ display: "block" }}
                            >
                              {t("form:entryExitDate")}
                            </label>
                            <label className="preview-subtitle">
                              {dayjs(currentMember?.visaEntryDate).format(
                                getDateFormat[router?.locale]
                              )}
                              {" - "}
                              {dayjs(currentMember?.visaExitDate).format(
                                getDateFormat[router?.locale]
                              )}
                            </label>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <div className="row">
                  <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                    <table>
                      <tbody>
                        <tr>
                          <td>
                            <GiPassport size={30} className="icon-purple" />{" "}
                          </td>
                          <td>
                            <label
                              className="text-muted preview-title fw-bold"
                              style={{ display: "block" }}
                            >
                              {t("form:passport")}
                            </label>
                            <label className="preview-subtitle">
                              {currentMember?.passportNumber}
                            </label>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                    <table>
                      <tbody>
                        <tr>
                          <td>
                            <BsCalendarDate
                              size={30}
                              className="icon-purple me-2"
                            />{" "}
                          </td>
                          <td>
                            <label
                              className="text-muted fw-bold preview-title"
                              style={{ display: "block" }}
                            >
                              {t("form:passportIssueDate")}
                            </label>
                            <label className="preview-subtitle">
                              {dayjs(currentMember?.passportIssueDate).format(
                                getDateFormat[router?.locale]
                              )}
                            </label>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                    <table>
                      <tbody>
                        <tr>
                          <td>
                            <BsCalendarDate
                              size={30}
                              className="icon-purple me-2"
                            />{" "}
                          </td>
                          <td>
                            <label
                              className="text-muted fw-bold preview-title"
                              style={{ display: "block" }}
                            >
                              {t("form:passportExpireDate")}
                            </label>
                            <label className="preview-subtitle">
                              {dayjs(currentMember?.passportExpireDate).format(
                                getDateFormat[router?.locale]
                              )}
                            </label>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div className="col-12">
                <Divider></Divider>
                <h6 className="text-purple fw-bold">
                  {t("form:personalInfo")}
                </h6>
                <div className="row">
                  <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                    <div className="d-flex flex-row align-items-center">
                      <div>
                        <BsFillFilePersonFill
                          size={30}
                          className="icon-purple"
                        />
                      </div>
                      <div className="d-flex flex-column ms-2 slf-preview-width">
                        <label className="text-muted fw-bold preview-title">
                          {t("form:nameSurname")}
                        </label>
                        <span className="preview-subtitle">
                          {currentMember?.name} {currentMember?.surname}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                    <div className="d-flex flex-row align-items-center">
                      <div>
                        <FaGlobeEurope size={30} className="icon-purple" />
                      </div>
                      <div className="d-flex flex-column ms-2 slf-preview-width">
                        <label className="text-muted fw-bold preview-title">
                          {t("form:nationality")}
                        </label>
                        <span className="preview-subtitle">
                          {currentMember?.nationality.name}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                    <table>
                      <tbody>
                        <tr>
                          <td>
                            <BsCalendarMonth
                              size={30}
                              className="icon-purple me-2"
                            />{" "}
                          </td>
                          <td>
                            <label
                              className="text-muted fw-bold preview-title"
                              style={{ display: "block" }}
                            >
                              {t("form:birthdate")}
                            </label>
                            <label className="preview-subtitle">
                              {dayjs(currentMember?.birthDate).format(
                                getDateFormat[router?.locale]
                              )}
                            </label>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div className="col-lg-4 col-md-6 col-sm-6 mt-4">
                    <div className="d-flex flex-row align-items-center">
                      <div>
                        <MdFamilyRestroom size={30} className="icon-purple" />
                      </div>
                      <div className="d-flex flex-column ms-2 slf-preview-width">
                        <label className="text-muted fw-bold preview-title">
                          {t("form:fatherMotherName")}
                        </label>
                        <span className="preview-subtitle">
                          {currentMember?.fatherName} -{" "}
                          {currentMember?.motherName}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-12">
                <Divider></Divider>
                <h6 className="text-purple fw-bold">{t("form:files")}</h6>
                <div className="row">
                  {currentMember?.memberFiles?.map((item) => (
                    <div
                      className="col-lg-6 col-md-6 col-sm-12 mt-4"
                      key={item.id}
                    >
                      <div className="card">
                        <div className="card-body">
                          <div className="d-flex">
                            <div className="preview-subtitle">
                              <BsFillFileEarmarkFill
                                size={30}
                                className="icon-purple me-2"
                              />{" "}
                              {item.fileType.displayValue}
                            </div>
                            <div className="ms-auto pt-1">
                              <Button
                                type="text"
                                onClick={() =>
                                  showPreview(
                                    item.fileType.id,
                                    item.fileType.displayValue
                                  )
                                }
                              >
                                {" "}
                                <BiShow className="icon-purple me-2" />{" "}
                                {t("site:show")}
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-4 mb-4">
          <Checkbox onChange={onChangeCheckBox} id="checkbox">
            {t("site:previewConfirm")}
          </Checkbox>
        </div>

        <>
          <div className="row">
            <div className="col-6">
              <Button
                size="large"
                onClick={() => {
                  dispatch(setStep(2));
                }}
                className="btn stepper-button-back"
              >
                {t("site:back")}
              </Button>
            </div>
            {canProceed ? (
              <div className="col-6 text-end">
                <Button
                  size="large"
                  onClick={() => {
                    movePaymentStep();
                  }}
                  className="btn stepper-button-next"
                  loading={loadingAction}
                >
                  {t("site:continue")}
                </Button>
              </div>
            ) : (
              <></>
            )}
          </div>
        </>
      </div>
    </div>
  );
};

export default Preview;
