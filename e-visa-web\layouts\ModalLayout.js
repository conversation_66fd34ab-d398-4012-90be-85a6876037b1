import {  useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { useState } from "react";
import localeTR from "antd/locale/tr_TR";
import localeEN from "antd/locale/en_US";
import useTranslation from "next-translate/useTranslation";

const ModalLayout = ({ children }) => {
  const [setLocale] = useState(localeEN);
  const { data: session, status } = useSession();
  const { t } = useTranslation();
  function dChange(e) {
    localStorage.setItem("locale", e);
    setLocale(localeTR);
    route.push("", "", { locale: e });
  }
  const route = useRouter();
  const SSOLogout = () => {
    if (session == null) {
      return res.redirect("/");
    }

    const endSessionURL = `${process.env.IdentityServer4_Issuer}/connect/endsession`;
    const redirectURL = `${process.env.NEXTAUTH_URL}/logout/`;
    const token = session.idToken;
    const fullUrl = `${endSessionURL}?id_token_hint=${token}&post_logout_redirect_uri=${redirectURL}`;

    window.location.replace(fullUrl);
  };

  return <>{children}</>;
};

export default ModalLayout;
