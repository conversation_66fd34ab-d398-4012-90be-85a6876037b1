import { <PERSON><PERSON>, But<PERSON>, Checkbox, Divider, Row, Select, Spin } from "antd";
import React, { useLayoutEffect, useState } from "react";
import api from "../../pages/api/helpers";
import { useSession } from "next-auth/react";
import axioshelper, {
  BaseUrlType,
  MethodType,
} from "../../pages/api/axioshelper";
import { useDispatch, useSelector } from "react-redux";
import { setPrecondition } from "../../store/reducer/applicaitonSlice";
import { setStep } from "../../store/reducer/stepperSlice";
import useStateRef from "react-usestateref";
import useTranslation from "next-translate/useTranslation";
import { LoadingOutlined } from "@ant-design/icons";
import { useRouter } from "next/router";
import { FaGlobeEurope } from "react-icons/fa";

const Precondition = () => {
  React.useLayoutEffect = React.useEffect;

  const dispatch = useDispatch();
  const [countries, setCountries, countriesRef] = useStateRef([]);
  const [passportTypes, setPassportTypes] = useState([]);
  const [visaTypes, setVisaTypes] = useState([]);
  const [questions, setQuestions] = useState([]);
  const [preconditions, setPreconditions, preconditionsRef] = useStateRef([]);
  const [unkownInfo, setUnknownInfo] = useState(false);
  const [canProceed, setCanProceed] = useState(false);
  const [loading, setLoading] = useState(true);
  const [allChecked, setAllChecked] = useState(false);
  const [
    selectedPassportType,
    setSelectedPassportType,
    selectedPassportTypeRef,
  ] = useStateRef(null);
  const [selectedVisaType, setSelectedVisaType, selectedVisaTypeRef] =
    useStateRef(null);
  const [
    selectedPrecondition,
    setSelectedPrecondition,
    selectedPreconditionRef,
  ] = useStateRef();
  const [isPostback, setIsPostback] = useState(false);
  const { t } = useTranslation();
  const router = useRouter();
  const { status } = useSession();
  var preconditionData = useSelector(
    (state) => state.application.preconditionData
  );
  var investigation = useSelector((state) => state.application.investigation);
  const [selectedCountry, setSelectedCountry, selectedCountryRef] =
    useStateRef(null);

  useLayoutEffect(() => {
    const fetchData = async () => {
      var result = await api.GetCountries();
      setCountries(result?.data);
      setLoading(false);
    };
    if (status == "authenticated") {
      fetchData();
    }
  }, [status, router.locale]);

  useLayoutEffect(() => {
    if (
      preconditionData?.countryId > 0 &&
      preconditionData.passportTypeId > 0 &&
      preconditionData.visaTypeId > 0 &&
      !isPostback
    ) {
      setIsPostback(true);
      setInital();
    }
  }, [preconditionData, router.locale]);

  useLayoutEffect(() => {
    if (
      preconditionData?.countryId > 0 &&
      preconditionData.passportTypeId > 0 &&
      preconditionData.visaTypeId > 0
    ) {
      setInital();
    }
  }, [router.locale]);

  const setInital = async () => {
    const countryResult = await axioshelper(
      MethodType.GET,
      BaseUrlType.Public_API,
      `preconditions/${preconditionData?.countryId}`,
      null
    );
    setSelectedCountry(preconditionData?.countryId);
    if (countryResult?.status == "SUCCESS") {
      setUnknownInfo(false);
      setPreconditions(countryResult?.data.preconditions);
      setPassportTypes(countryResult?.data.passportTypes);
      setQuestions(countryResult?.data.questions);
    } else {
      setUnknownInfo(true);
      return;
    }
    setSelectedPassportType(preconditionData.passportTypeId);

    var precon = preconditionsRef?.current?.find(
      (f) =>
        f.passportTypeId == preconditionData.passportTypeId &&
        f.countryId == selectedCountryRef?.current
    );
    setSelectedPrecondition(precon);
    const passportResult = await axioshelper(
      MethodType.GET,
      BaseUrlType.Public_API,
      `visa/conditional-types/${precon.id}`,
      null
    );

    if (passportResult.status == "SUCCESS") {
      setVisaTypes(passportResult?.data);
      setUnknownInfo(false);
      setSelectedVisaType(preconditionData?.visaTypeId);
    } else {
      setUnknownInfo(true);
    }
  };

  var onCountryChange = async (id) => {
    setLoading(true);
    setPassportTypes([]);
    setPreconditions([]);
    setSelectedPrecondition(null);
    setSelectedPassportType(null);
    setCanProceed(false);
    setSelectedVisaType(null);
    const result = await axioshelper(
      MethodType.GET,
      BaseUrlType.Public_API,
      `preconditions/${id}`,
      null
    );
    setLoading(false);
    setSelectedCountry(id);
    if (result?.status == "SUCCESS") {
      setUnknownInfo(false);
      setPreconditions(result?.data.preconditions);
      setPassportTypes(result?.data.passportTypes);
      setQuestions(result?.data.questions);

      if (preconditionData != null && !isPostback) {
        onPassportTypeChange(preconditionData?.passportTypeId);
      }
    } else {
      setUnknownInfo(true);
    }
  };

  var onPassportTypeChange = async (id) => {
    setVisaTypes([]);
    setCanProceed(false);
    setSelectedVisaType(null);
    var precon = preconditionsRef?.current?.find(
      (f) =>
        f.passportTypeId == id && f.countryId == selectedCountryRef?.current
    );
    setSelectedPassportType(id);
    setSelectedPrecondition(precon);

    if (precon != null && precon.visaInfoId == 1 && precon?.isMobileOnly) {
      let clone = structuredClone(precon);
      clone.visaInfo.displayValue = t("site:isMobileOnlyDV");
      clone.displayMessage = t("site:isMobileOnlyDM");
      setSelectedPrecondition(clone);
    } else if (precon != null && precon.visaInfoId == 1) {
      const result = await axioshelper(
        MethodType.GET,
        BaseUrlType.Public_API,
        `visa/conditional-types/${precon.id}`,
        null
      );
      if (result?.status == "SUCCESS") {
        setVisaTypes(result?.data);
        setUnknownInfo(false);

        if (precon != null && !isPostback) {
          onVisaTypeChange(preconditionData?.visaTypeId);
        }
      } else {
        setUnknownInfo(true);
      }
    }
  };

  var onVisaTypeChange = async (id) => {
    var precoditionData = {
      countryId: selectedCountryRef?.current,
      passportTypeId: selectedPassportTypeRef?.current,
      visaTypeId: id,
      precondition: selectedPreconditionRef.current,
    };
    dispatch(setPrecondition(precoditionData));
    setSelectedVisaType(id);
    if ((questions != null && questions.length == 0) || allChecked) {
      //onay verilecek soru yoksa ilerleyebilsin
      if (id > 0) setCanProceed(true);
    }
  };

  const answeredQuestions = (checkedValues) => {
    if (checkedValues.length == questions.length) {
      setAllChecked(true);
      selectedVisaTypeRef?.current > 0
        ? setCanProceed(true)
        : setCanProceed(false);
    } else {
      setCanProceed(false);
      setAllChecked(false);
    }
  };

  function getType() {
    var type = "info";
    if (
      selectedPrecondition?.visaInfoId == 1 &&
      selectedPrecondition?.isMobileOnly === true
    ) {
      //evisa ama sadece mobile appden başvuru kabul ediyorsa
      type = "warning";
    } else if (selectedPrecondition?.visaInfoId == 1) {
      //evisa
      type = "success";
    } else if (selectedPrecondition?.visaInfoId == 2) {
      //muaf
      type = "info";
    }
    if (selectedPrecondition?.visaInfoId == 3) {
      //sticker
      type = "warning";
    }
    return type;
  }

  const antIcon = (
    <LoadingOutlined
      style={{
        fontSize: 24,
      }}
      spin
    />
  );
  return (
    <Spin indicator={antIcon} spinning={loading}>
      <div className="row" key={investigation.currentMemberId}>
        <div className="col-lg-6 col-md-6 col-sm-12 mb-4">
          <p>
            {t("form:nationality")} <span className="text-danger">*</span>
          </p>
          <Select
            showSearch
            size="large"
            style={{ width: "100%" }}
            optionFilterProp="children"
            filterOption={(input, option) =>
              (option?.label.toLowerCase() ?? "").includes(
                input.toLocaleLowerCase()
              )
            }
            filterSort={(optionA, optionB) =>
              (optionA?.label ?? "")
                .toLowerCase()
                .localeCompare((optionB?.label ?? "").toLowerCase())
            }
            options={countriesRef?.current?.map((country) => {
              return { value: country.id, label: country.name };
            })}
            onChange={onCountryChange}
            value={selectedCountry > 0 ? selectedCountry : null}
          />
        </div>
        <div className="col-lg-6 col-md-6 col-sm-12 mb-4">
          <p>
            {t("form:passportType")} <span className="text-danger">*</span>{" "}
          </p>
          <Select
            showSearch
            size="large"
            style={{ width: "100%" }}
            options={passportTypes?.map((passport) => {
              return { value: passport.id, label: passport.name };
            })}
            onChange={onPassportTypeChange}
            value={selectedPassportType > 0 ? selectedPassportType : null}
          />
        </div>
      </div>
      {unkownInfo ? (
        <div className="card text-white bg-light mb-3">
          <div className="card-body text-dark">
            <h6 className="card-title text-center py-5">
              {t("site:unavailableVisaText")}
            </h6>
            <p className="card-text"></p>
          </div>
        </div>
      ) : (
        <div>
          {selectedPrecondition ? (
            <>
              <div className="row">
                <div className={`col-12 py-4 text-${getType()}`}>
                  <div className="d-flex">
                    <FaGlobeEurope
                      style={{ fontSize: 24 }}
                      className="me-2"
                    ></FaGlobeEurope>{" "}
                    {t("site:visaInfo")}:{" "}
                    {selectedPrecondition?.visaInfo?.displayValue}
                  </div>
                </div>
                <div className="col-12">
                  <Alert
                    message={t("site:infoNote")}
                    description={selectedPrecondition?.displayMessage}
                    type={getType()}
                    showIcon
                  />
                </div>
              </div>
              <Divider></Divider>
            </>
          ) : null}

          {selectedPrecondition?.visaInfoId == 1 &&
          selectedPrecondition?.isMobileOnly !== true ? (
            <div className="row">
              <div className="col-lg-6 col-md-6 col-sm-12 mb-4">
                <p>
                  {t("site:visitPurpose")}{" "}
                  <span className="text-danger">*</span>{" "}
                </p>
                <Select
                  showSearch
                  size="large"
                  style={{ width: "100%" }}
                  options={visaTypes?.map((visa) => {
                    return { value: visa.id, label: visa.name };
                  })}
                  onChange={onVisaTypeChange}
                  value={selectedVisaType > 0 ? selectedVisaType : null}
                />
              </div>
              {questions?.length > 0 ? (
                <div className="col-12 mt-4 mb-4">
                  <h6 className="mb-2">{t("site:questionConditionText")}</h6>
                  <Checkbox.Group onChange={answeredQuestions}>
                    {questions.map((q) => (
                      <Row key={q.id}>
                        <Checkbox
                          checked={preconditionData?.visaTypeId > 0}
                          value={q.id}
                        >
                          {q.definition}
                        </Checkbox>
                      </Row>
                    ))}
                  </Checkbox.Group>
                </div>
              ) : null}
            </div>
          ) : null}
        </div>
      )}

      {canProceed && selectedVisaType != null ? (
        <div className="row">
          <div className="col-6 px-5"></div>
          <div className="col-6 text-end">
            <Button
              size="large"
              onClick={() => {
                dispatch(setStep(1));
              }}
              className="btn stepper-button-next"
            >
              {t("site:continue")}
            </Button>
          </div>
        </div>
      ) : null}
    </Spin>
  );
};

export default Precondition;
