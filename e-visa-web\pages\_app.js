import "/styles/globals.css";
import { ConfigProvider } from "antd";
import "dayjs/locale/tr";
import localeTR from "antd/locale/tr_TR";
import localeEN from "antd/locale/en_US";
import { useRouter } from "next/router";
import { SessionProvider, useSession } from "next-auth/react";
import "bootstrap/dist/css/bootstrap.min.css";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { store } from "../store/store";
import { Provider } from "react-redux";
import Loader from "../components/loader";
import CheckSession from "../components/check-session";
import Layoutv2 from "../layouts/Layoutv2";

export default function App({
  Component,
  pageProps: { session, ...pageProps },
}) {
  const route = useRouter();
  return (
    <>
      <Provider store={store}>
        <ConfigProvider locale={route.locale == "tr" ? localeTR : localeEN}>
          <SessionProvider session={session}>
            {Component.PageLayout ? (
              <Component.PageLayout>
                <Component {...pageProps} />
              </Component.PageLayout>
            ) : (
              <Layoutv2>
                <Loader>
                  <CheckSession />
                  <Component {...pageProps} />
                </Loader>
                <ToastContainer autoClose={3000} />
              </Layoutv2>
            )}
          </SessionProvider>
        </ConfigProvider>
      </Provider>
    </>
  );
}

function Auth({ children }) {
  // if `{ required: true }` is supplied, `status` can only be "loading" or "authenticated"
  const { status } = useSession({ required: true });
  const route = useRouter();

  if (status === "loading") {
    return null;
  }

  return children;
}
