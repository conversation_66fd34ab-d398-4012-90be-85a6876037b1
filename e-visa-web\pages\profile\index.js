import { Button, Form, Input, Upload, Modal, Divider } from "antd";
import { useSession } from "next-auth/react";
import useTranslation from "next-translate/useTranslation";
import axioshelper, { BaseUrlType, MethodType } from "../api/axioshelper";
import { useEffect, useState } from "react";
import showSweetAlert from "../../utils/sweetAlert";
import { useRouter } from "next/router";
import ImgCrop from "antd-img-crop";
import { PlusOutlined } from "@ant-design/icons";
import { toast } from "react-toastify";

const Profile = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const { status } = useSession();
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const [profileImage, setProfileImage] = useState();
  const handleCancel = () => setPreviewOpen(false);
  const [fileList, setFileList] = useState([]);
  const [form] = Form.useForm();
  const [loadingActionProfile, setLoadingActionProfile] = useState(false);
  const [loadingActionPassword, setLoadingActionPassword] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      getProfileImage();
    };

    if (status == "authenticated") {
      fetchData();
    }
  }, [status]);

  const getProfileImage = async () => {
    var result = await axioshelper(
      MethodType.GET,
      BaseUrlType.Public_API,
      `user/profile`,
      null
    );

    if (result?.status == "SUCCESS") {
      form.setFieldsValue({
        profile: {
          name: result.data?.name != null ? result.data?.name : "",
          surname: result.data?.surname != null ? result.data?.surname : "",
          phone: result.data?.tel != null ? result.data?.tel : "",
        },
      });

      if (result.data.profilePictureName != null) {
        var uzanti = result.data.profilePictureName.substring(
          result.data.profilePictureName.lastIndexOf(".") + 1
        );

        var fileContent = `data:image/${uzanti};base64, ${result.data.profilePicture}`;
        setProfileImage(fileContent);

        var f = {
          uid: 1,
          name: result.data.profilePictureName,
          status: "done",
          url: fileContent,
        };
        var f_list = [];
        f_list.push(f);
        setFileList(f_list);
      }
    }
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div
        style={{
          marginTop: 8,
          width: "100%",
        }}
      >
        {t("site:upload")}
      </div>
    </div>
  );

  const getBase64 = (file) =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });

  const onFinish = async (data) => {
    setLoadingActionProfile(true);
    var model = {
      name: data.profile.name,
      surname: data.profile.surname,
      tel: data.profile.phone,
    };

    var result = await axioshelper(
      MethodType.PUT,
      BaseUrlType.Public_API,
      "user/profile",
      model
    );

    if (result?.status == "SUCCESS") {
      showSweetAlert(t("site:success"), t("site:successMessagev2"), "success");
    }
    setLoadingActionProfile(false);
  };

  const updatePassword = async (data) => {
    setLoadingActionPassword(true);
    var result = await axioshelper(
      MethodType.POST,
      BaseUrlType.Public_API,
      "user/change-password",
      data.password
    );

    if (result?.status == "SUCCESS") {
      showSweetAlert(t("site:success"), t("site:successMessagev2"), "success");
    } else {
      if (result.data?.validationMessages.length > 0) {
        result.data?.validationMessages.map((err) =>
          toast(err, { autoClose: 5000, type: "warning", theme: "light" })
        );
      }
    }
    setLoadingActionPassword(false);
  };

  const setImage = async (file) => {
    var f = {
      uid: file.uid,
      name: file.name,
      status: "done",
      url: await getBase64(file),
    };

    var f_list = [];
    f_list.push(f);
    setFileList(f_list);
  };

  const handlePreview = async (file) => {
    setPreviewImage(file.url);
    setPreviewOpen(true);
  };

  const updateImage = async (file) => {
    if (file.status != "removed") {
      var content = await getBase64(file);
      var data = content.split(",").pop();
      var model = {
        fileName: file.name,
        fileData: data,
      };

      var fileResult = await axioshelper(
        MethodType.POST,
        BaseUrlType.Public_API,
        "user/change-picture",
        model
      );
      if (fileResult.status == "SUCCESS") {
        setImage(file);
        toast.success(t("site:successMessagev2"));
      }
    }
  };

  return (
    <>
      <Modal
        open={previewOpen}
        footer={null}
        onCancel={handleCancel}
        title={t("site:profilePhoto")}
      >
        <img
          alt="example"
          style={{
            width: "100%",
          }}
          src={previewImage}
        />
      </Modal>
      <div className="container slf-container">
        <div className="pt-4">
          <div className="col">
            <h3>{t("site:myAccount")}</h3>
            <p className="fst-italic">{t("site:profileDescription")}</p>
          </div>
          <div className="card bg-transparent m-1">
            <div className="card-body">
              <div className="row">
                <div className="col-lg-4 col-md-4 col-sm-12">
                  <div className="row">
                    <div className="col-12 pt-5" style={{ height: 300 }}>
                      <ImgCrop rotationSlider aspect={1 / 1}>
                        <Upload
                          listType="picture-circle"
                          onPreview={handlePreview}
                          fileList={fileList}
                          beforeUpload={(file) => {
                            updateImage(file);
                            return false;
                          }}
                          onChange={(e) => {
                            if (e.file.status == "removed") {
                              setFileList([]);
                            }
                          }}
                          accept="image/png, image/jpeg"
                          maxCount={1}
                        >
                          {fileList.length == 0 ? uploadButton : null}
                        </Upload>
                      </ImgCrop>
                    </div>
                  </div>
                  <></>
                </div>
                <div className="col-lg-8 col-md-8 col-sm-12">
                  <Form
                    form={form}
                    name="frm-profile"
                    labelCol={{
                      span: 12,
                    }}
                    size="large"
                    onFinish={onFinish}
                    wrapperCol={{
                      span: 32,
                    }}
                    layout="vertical"
                  >
                    <div className="row">
                      <div className="col-lg-4 col-md-4 col-sm-12">
                        <Form.Item
                          name={["profile", "name"]}
                          label={t("form:name")}
                          rules={[
                            {
                              required: true,
                              max: 50,
                            },
                          ]}
                        >
                          <Input maxLength={50} />
                        </Form.Item>
                      </div>
                      <div className="col-lg-4 col-md-4 col-sm-12">
                        <Form.Item
                          name={["profile", "surname"]}
                          label={t("form:surname")}
                          rules={[
                            {
                              required: true,
                              max: 50,
                            },
                          ]}
                        >
                          <Input maxLength={50} />
                        </Form.Item>
                      </div>
                      <div className="ccol-lg-4 col-md-4 col-sm-12">
                        <Form.Item
                          name={["profile", "phone"]}
                          label={t("form:phone")}
                          rules={[
                            {
                              required: true,
                              max: 20,
                            },
                            { type: "phone" },
                          ]}
                        >
                          <Input maxLength={20} />
                        </Form.Item>
                      </div>
                    </div>
                    <div className="row mt-4">
                      <div className="col text-end">
                        <Button
                          size="large"
                          htmlType="submit"
                          className="btn update-profile"
                          loading={loadingActionProfile}
                        >
                          {t("site:updateProfile")}
                        </Button>
                      </div>
                    </div>
                  </Form>
                  <Divider className="my-4"></Divider>
                  <Form
                    name="frm-password"
                    labelCol={{
                      span: 12,
                    }}
                    size="large"
                    onFinish={updatePassword}
                    wrapperCol={{
                      span: 32,
                    }}
                    layout="vertical"
                  >
                    <div className="row">
                      <div className="col-lg-6 col-md-6 col-sm-12">
                        <Form.Item
                          name={["password", "currentPassword"]}
                          label={t("site:currentPassoword")}
                          rules={[
                            {
                              required: true,
                              max: 50,
                            },
                          ]}
                        >
                          <Input.Password maxLength={50} />
                        </Form.Item>
                      </div>
                      <div className="col-lg-6 col-md-6 col-sm-12">
                        <Form.Item
                          name={["password", "newPassword"]}
                          label={t("site:newPassoword")}
                          rules={[
                            {
                              required: true,
                              max: 50,
                            },
                          ]}
                        >
                          <Input.Password maxLength={50} />
                        </Form.Item>
                      </div>
                    </div>
                    <div className="row mt-4">
                      <div className="col text-end">
                        <Button
                          size="large"
                          htmlType="submit"
                          className="btn update-profile"
                          loading={loadingActionPassword}
                        >
                          {t("site:updatePassword")}
                        </Button>
                      </div>
                    </div>
                  </Form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Profile;
