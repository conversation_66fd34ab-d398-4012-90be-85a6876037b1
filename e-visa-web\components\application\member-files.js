import { CheckCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { <PERSON>ton, Collapse, Modal, Upload } from "antd";
import React, { useEffect, useLayoutEffect, useState } from "react";
import api from "../../pages/api/helpers";
import useStateRef from "react-usestateref";
import axioshelper, {
  BaseUrlType,
  MethodType,
} from "../../pages/api/axioshelper";
import { toast } from "react-toastify";
import { useDispatch, useSelector } from "react-redux";
import { setStep } from "../../store/reducer/stepperSlice";
import { setMemberFiles } from "../../store/reducer/applicaitonSlice";
import useTranslation from "next-translate/useTranslation";
import { useRouter } from "next/router";

const getBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });

const { Panel } = Collapse;

const MemberFiles = () => {
  React.useLayoutEffect = React.useEffect;

  const dispatch = useDispatch();
  var router = useRouter();
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const [isImage, setIsImage] = useState(true);
  const [previewTitle, setPreviewTitle] = useState("");
  const [fileTypes, setFileTypes, fileTypesRef] = useStateRef([]);
  const [canProceed, setCanProceed] = useStateRef(false);
  const { t } = useTranslation();
  const handleCancel = () => setPreviewOpen(false);
  const [canBack, setCanBack] = useStateRef(true);
  var investigation = useSelector((state) => state.application.investigation);
  var precondition = useSelector((state) => state.application.preconditionData);
  var countryId = precondition?.countryId;
  const currentStep = useSelector((state) => state.stepper.value);

  useEffect(() => {
    setFileTypes([]);
    setCanProceed(false);
    const fetchData = async () => {
      var existingMember = investigation.members.find(
        (e) => e.investigationMemberId == investigation.currentMemberId
      );

      if (existingMember?.memberStatus?.id == 3) setCanBack(false);

      var result = await api.GetCountryFiles(countryId);
      if (result?.status == "SUCCESS") {
        var fTypes = [];
        var data = result.data;

        for (let index = 0; index < data.length; index++) {
          var ftype = {};
          var fileType = data[index];

          var existingMemberFile = existingMember?.files.find(
            (e) => e.id == fileType.id && e?.state == 2
          );

          if (existingMemberFile) {
            var file = existingMemberFile?.file;

            if (existingMemberFile?.file == null) {
              var fileResult = await axioshelper(
                MethodType.GET,
                BaseUrlType.Public_API,
                `investigations/${investigation.id}/members/${investigation.currentMemberId}/files/${existingMemberFile?.fileId}`,
                null
              );
              if (fileResult.status == "SUCCESS") {
                var meta = getFileMetaSrc(fileResult.data.fileName);
                file = {
                  uid: 1,
                  url: meta + fileResult.data.fileContent,
                  name: fileResult.data.fileName,
                  status: "done",
                };
              }
            }
            
            ftype = {
              id: fileType.id,
              explanation: fileType.explanation,
              state: 2, //0-bekliyor 1- selected 2-başarılı yüklendi 3- hata
              file: file,
              fileId: existingMemberFile?.fileId,
            };
          } else {
            ftype = {
              id: fileType.id,
              explanation: fileType.explanation,
              state: 0,
              file: {
                uid: fileType.id,
                status: "removed",
              },
            };
          }

          fTypes.push(ftype);
        }

        setFileTypes(fTypes);

        var filterWaiting = fileTypesRef.current.filter(
          (f) => f.state != 2 && f.id != 5
        ); //başarılı olmayanlar
        if (filterWaiting.length == 0) {
          setCanProceed(true);
        } else {
          setCanProceed(false);
        }
      }
    };
    if (countryId > 0 && currentStep == 2) {
      fetchData();
    }
    // setCurrentMemberId(investigation?.currentMemberId);
  }, [investigation?.currentMemberId, countryId, router.locale, currentStep]);

  const handlePreview = async (id, title) => {
    var fileInfo = fileTypesRef.current.find((f) => f.id == id);
    if (fileInfo != null) {
      if (fileInfo.file.url.includes("data:application/pdf")) {
        setIsImage(false);
      } else {
        setIsImage(true);
      }
      setPreviewImage(fileInfo.file.url || fileInfo.file.preview);
      setPreviewOpen(true);
      setPreviewTitle(title);
    }
  };

  const AddFilesToList = async (id, file, explanation) => {
    if (file.status == "removed") {
      var updated = fileTypesRef.current.map((obj) =>
        obj.id == id ? { ...obj, state: 0, file: null } : obj
      );
      setFileTypes(updated);
      setCanProceed(false);
    } else {
      var base64File = await getBase64(file);
      var fModel = {
        uid: id,
        url: base64File,
        name: file.name,
        status: "done",
      };
      var updated = fileTypesRef.current?.map((obj) =>
        obj.id == id ? { ...obj, state: 1, file: fModel } : obj
      );
      setFileTypes(updated);
      uploadToServer(id, fModel, explanation);
    }
  };

  const uploadToServer = async (id, filemodel, explanation) => {
    var data = filemodel.url.split(",").pop();
    var model = {
      fileType: id,
      fileName: filemodel.name,
      fileData: data,
    };

    setCanProceed(false);
    var fileResult = await axioshelper(
      MethodType.POST,
      BaseUrlType.Public_API,
      `investigations/${investigation.id}/members/${investigation.currentMemberId}/files`,
      model
    );
    if (fileResult.status == "SUCCESS") {
      var updated = fileTypesRef.current?.map((obj) =>
        obj.id == id ? { ...obj, state: 2, file: filemodel } : obj
      );
      setFileTypes(updated);
      toast.success(t("site:fileUploadSuccess", { fileType: explanation }));
      setCanProceed(true);
      var memberFiles = {
        investigationMemberId: investigation.currentMemberId,
        files: fileTypesRef.current,
      };
      dispatch(setMemberFiles(memberFiles));

      var filterWaiting = fileTypesRef.current.filter(
        (f) => f.state != 2 && f.id != 5
      ); //başarılı olmayanlar

      if (filterWaiting.length > 0) {
        setCanProceed(false);
      } else {
        setCanProceed(true);
      }
    } else {
      var updated = fileTypesRef.current?.map((obj) =>
        obj.id == id ? { ...obj, state: 3, file: filemodel } : obj
      );
      setFileTypes(updated);
      toast.error(t("site:errorMessage", { operation: explanation }));
    }
  };

  function getFileMetaSrc(fileName) {
    var uzanti = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
    if (uzanti == ".jpg" || uzanti == ".jpeg" || uzanti == ".png") {
      uzanti = uzanti.replace(".", "");
      return `data:image/${uzanti};base64, `;
    } else if (uzanti == ".pdf") {
      return `data:application/pdf;base64, `;
    } else {
      return "unsupported";
    }
  }

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div
        style={{
          marginTop: 8,
        }}
      >
        {t("site:upload")}
      </div>
    </div>
  );

  return (
    <div key={investigation?.currentMemberId}>
      <div key={investigation?.currentMemberId}>
        <div className="slf-member-files-wrap">
          {fileTypesRef.current?.filter((f)=>f.id != 5)?.map((file) => (
            <div className="slf-member-files" key={file.id}>
                <div className="card bg-transparent" key={file.id}>
                  <div className="card-body">
                    <div className="d-flex">
                      <div
                        className={
                          file.state == 3
                            ? " align-middle border-danger"
                            : " align-middle"
                        }
                      >
                        <Upload
                          key={file.id}
                          listType="picture-card"
                          onPreview={() => {
                            handlePreview(file.id, file.explanation.trim());
                          }}
                          onChange={(e) =>
                            AddFilesToList(file.id, e.file, file.explanation)
                          }
                          beforeUpload={(e) => {
                            return false;
                          }}
                          defaultFileList={
                            fileTypesRef?.current?.filter(
                              (f) => f.id == file.id && f.state == 2
                            ).length > 0
                              ? fileTypesRef?.current
                                  ?.filter((f) => f.id == file.id)
                                  ?.map((obj) => {
                                    return obj.file ?? null;
                                  })
                              : []
                          }
                          maxCount={1}
                          accept="image/png, image/jpeg, application/pdf"
                        >
                          {file.state == 0 ? uploadButton : null}
                        </Upload>
                        <span className="align-middle pl-3">
                          {file.explanation.trim()}
                        </span>
                      </div>
                      <div className="ms-auto pt-1">
                        <CheckCircleOutlined
                          style={{
                            color:
                              file.state == 2
                                ? "green"
                                : file.state == 3
                                ? "red"
                                : "",
                            fontSize: "18px",
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
            </div>
          ))}
        </div>
      </div>

      <Modal
        className={isImage ? "" : "w-50"}
        open={previewOpen}
        title={previewTitle}
        footer={null}
        onCancel={handleCancel}
      >
        {isImage ? (
          <img
            alt="example"
            style={{
              width: "100%",
            }}
            src={previewImage}
          />
        ) : (
          <embed
            style={{ width: "100%", minHeight: 450 }}
            src={previewImage}
          ></embed>
        )}
      </Modal>

      <div className="d-flex mt-4">
        {canBack ? (
          <div className="col-6">
            <Button
              size="large"
              onClick={() => {
                dispatch(setStep(1));
              }}
              className="btn stepper-button-back"
            >
              {t("site:back")}
            </Button>
          </div>
        ) : (
          <div className="col-6"></div>
        )}
        {canProceed ? (
          <div className="col-6 text-end">
            <Button
              size="large"
              onClick={() => {
                dispatch(
                  setMemberFiles({
                    investigationMemberId: investigation.currentMemberId,
                    files: fileTypesRef?.current,
                  })
                );
                dispatch(setStep(3));
              }}
              className="btn stepper-button-next"
            >
              {t("site:continue")}
            </Button>
          </div>
        ) : null}
      </div>
    </div>
  );
};
export default MemberFiles;
