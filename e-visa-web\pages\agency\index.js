import { useSession } from "next-auth/react";
import axioshelper, { BaseUrlType, MethodType } from "../api/axioshelper";
import useStateRef from "react-usestateref";
import { useEffect } from "react";
import {
  EnvironmentOutlined,
  MailOutlined,
  PhoneOutlined,
  ProfileOutlined,
} from "@ant-design/icons";
import useTranslation from "next-translate/useTranslation";

const Agency = () => {
  const { data: session, status } = useSession();
  const [agencies, setAgencies, agenciesRef] = useStateRef([]);
  const { t } = useTranslation();

  useEffect(() => {
    const fetchData = async () => {
      var model = {
        pagination: {
          pageNumber: 1,
          pageSize: 100,
          orderBy: "Id",
          ascending: true,
        },
      };
      var result = await axioshelper(
        MethodType.GET,
        BaseUrlType.Public_API,
        `agencies`,
        model
      );
      if (result?.status == "SUCCESS") {
        setAgencies(result.data);
      }
    };

    if (status == "authenticated") {
      fetchData();
    }
  }, [status]);

  const divStyle = {
    overflowY: "scroll",
    height: "90vh",
    position: "relative",
    paddingRight: "5px",
  };

  return (
    <div className="container slf-container">
      <div className="row py-4">
        <div className="col">
          <h3>{t("site:agency")}</h3>
          <p className="fst-italic">{t("site:agencyDescription")}</p>
        </div>
      </div>
      {agenciesRef?.current.map((c) => (
        <div className="card border-0 my-4" key={c.id}>
          <div className="card-body">
            <div className="row py-1">
              <div className="col">
                <div className="call-center-item">
                  <span className="pe-2">
                    <ProfileOutlined />
                  </span>
                  <label style={{ fontSize: "16px" }}>{c.name}</label>
                </div>
              </div>
              <div className="col">
                <div className="call-center-item">
                  <span className="pe-2">
                    <PhoneOutlined />
                  </span>
                  <label>
                    {c.tel1} {c.tel2 != null ? " - " + c.tel2 : ""}
                  </label>
                </div>
              </div>
            </div>
            <div className="row py-1">
              <div className="col">
                <div className="call-center-item">
                  <span className="pe-2">
                    <MailOutlined />
                  </span>
                  <label>{c.email}</label>
                </div>
              </div>
              <div className="col">
                <div className="call-center-item">
                  <span className="pe-2">
                    <EnvironmentOutlined />
                  </span>
                  <label>{c.address}</label>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Agency;
