import axios from "axios";
import { toast } from 'react-toastify';
const instance = axios.create();
instance.interceptors.request.use(
  function (config) {
     return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

instance.interceptors.response.use(
  function (response) {
    return response.data;
  },
  function (error) {
    console.log(error)
    if(error.response?.status == 422){
      if(error.response?.data?.validationMessages.length > 0){
        error.response?.data?.validationMessages.map((err)=> toast(err, {autoClose:10000, type:"warning", theme: "light"}))
      }
      else{
        toast(error.response?.data?.message, {autoClose:8000, type:"warning", theme: "light"})
      }
    }
    if(error.response?.status == 401)
    {
      toast("Unauthorized", {autoClose:5000, type:"error", theme: "colored"})
    }
    // if(error.response?.status == 404){
    //   toast(error.response?.data?.message, {autoClose:8000, type:"error", theme: "colored"})
    // }
    return Promise.resolve(error.response);
  }
);

export default instance;
