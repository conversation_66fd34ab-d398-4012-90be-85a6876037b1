import { useSession } from "next-auth/react";
import axioshelper, { BaseUrlType, MethodType } from "../api/axioshelper";
import useStateRef from "react-usestateref";
import { useEffect } from "react";
import { BiBuildingHouse } from "react-icons/bi";
import { BsEnvelopeCheck, BsFillTelephoneFill } from "react-icons/bs";
import useTranslation from "next-translate/useTranslation";

const CallCenter = () => {
  const { data: session, status } = useSession();
  const [callCenters, setCallCenters, callCentersRef] = useStateRef([]);
  const { t } = useTranslation();

  useEffect(() => {
    const fetchData = async () => {
      var model = {
        pagination: {
          pageNumber: 1,
          pageSize: 100,
          orderBy: "Id",
          ascending: true,
        },
      };
      var result = await axioshelper(
        MethodType.POST,
        BaseUrlType.Public_API,
        `callCenters/search`,
        model
      );

      if (result?.status == "SUCCESS") {
        setCallCenters(result.data);
      }
    };
    if (status == "authenticated") {
      fetchData();
    }
  }, [status]);

  const divStyle = {
    overflowY: "scroll",
    height: "90vh",
    position: "relative",
    paddingRight: "5px",
  };

  return (
    <div className="container slf-container">
      <div className="row py-3">
        <div className="col">
          <h3>Call Center</h3>
          <p className="fst-italic">{t("site:callCenterDescription")}</p>
        </div>
      </div>

      <div className="row">
        {callCentersRef?.current.map((c) => (
          <div className="col-lg-6 col-md-6 col-sm-12 mt-2" key={c.id}>
            <div
              className="card bg-transparent"
              style={{ border: "1px dashed #4C1D95" }}
            >
              <div className="card-body">
                <div className="row">
                  <div className="col-12 mb-3">
                    <BiBuildingHouse className="icon-purple me-2" size={40} />{" "}
                    <label className="fw-bold">{c.caption}</label>
                  </div>
                  <div className="col-6 px-3">
                    <BsFillTelephoneFill
                      className="icon-purple me-2"
                      size={20}
                    />{" "}
                    {c.tel1}
                  </div>
                  <div className="col-6 px-3">
                    <BsEnvelopeCheck className="icon-purple me-2" size={20} />{" "}
                    {c.email}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CallCenter;
