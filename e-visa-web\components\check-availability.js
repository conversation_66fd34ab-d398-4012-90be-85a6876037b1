import useTranslation from "next-translate/useTranslation";
import { But<PERSON>, Form, Alert, Divider, Select, Modal, Spin } from "antd";
import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useState } from "react";
import useStateRef from "react-usestateref";
import api from "../pages/api/helpers";
import axioshelper, { BaseUrlType, MethodType } from "../pages/api/axioshelper";
import { FaGlobeEurope } from "react-icons/fa";
import { BsDot } from "react-icons/bs";
import showSweetAlert from "../utils/sweetAlert";
import { LoadingOutlined } from "@ant-design/icons";

const CheckAvailability = () => {
  const { data: session, status } = useSession();
  const [authenticated, setAuthenticated] = useState(false);
  const [countries, setCountries, countriesRef] = useStateRef([]);
  const [preconditions, setPreconditions, preconditionsRef] = useStateRef([]);
  const [passportTypes, setPassportTypes] = useState([]);
  const [questions, setQuestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [detailOpen, setDetailOpen] = useState(false);
  const handleCancel = () => setDetailOpen(false);
  const [
    selectedPassportType,
    setSelectedPassportType,
    selectedPassportTypeRef,
  ] = useStateRef(null);
  const [
    selectedPrecondition,
    setSelectedPrecondition,
    selectedPreconditionRef,
  ] = useStateRef(null);

  const { t } = useTranslation();
  const [form] = Form.useForm();

  useEffect(() => {
    api.GetCountries().then((result) => {
      setCountries(result?.data);
    });

    if (status == "authenticated") {
      setAuthenticated(true);
    }
  }, [status]);

  var onCountryChange = async (id) => {
    setLoading(true);
    setPassportTypes([]);
    setPreconditions([]);
    setSelectedPrecondition(null);
    setSelectedPassportType(null);
    const result = await axioshelper(
      MethodType.GET,
      BaseUrlType.Public_API,
      `preconditions/${id}`,
      null
    );
    setLoading(false);
    if (result?.status == "SUCCESS") {
      setPreconditions(result?.data.preconditions);
      setPassportTypes(result?.data.passportTypes);
      setQuestions(result?.data.questions);
    } else {
      showSweetAlert(
        t("site:warning"),
        t("site:unavailableVisaText"),
        "warning",
        3000
      );
    }
  };

  var onPassportChange = async (id) => {
    var condition = preconditions.find((f) => f.passportTypeId == id);
    setSelectedPassportType(id);
    if (condition) {
      if (condition.visaInfoId == 1 && condition?.isMobileOnly) {
        let clone = structuredClone(condition);
        clone.visaInfo.displayValue = t("site:isMobileOnlyDV");
        clone.displayMessage = t("site:isMobileOnlyDM");
        setSelectedPrecondition(clone);
      } else {
        setSelectedPrecondition(condition);
      }
    } else {
      setSelectedPrecondition(null);
    }
  };

  const checkAvailabilityBtn = async () => {
    if (selectedPrecondition) setDetailOpen(true);
    else {
      showSweetAlert(
        t("site:warning"),
        t("site:checkAvailabilityWarning"),
        "warning",
        3000
      );
    }
  };

  const antIcon = (
    <LoadingOutlined
      style={{
        fontSize: 24,
      }}
    />
  );

  function getType() {
    var type = "info";
    if (
      selectedPrecondition?.visaInfoId == 1 &&
      selectedPrecondition?.isMobileOnly === true
    ) {
      //evisa ama sadece mobile appden başvuru kabul ediyorsa
      type = "warning";
    } else if (selectedPrecondition?.visaInfoId == 1) {
      //evisa
      type = "success";
    } else if (selectedPrecondition?.visaInfoId == 2) {
      //muaf
      type = "info";
    }
    if (selectedPrecondition?.visaInfoId == 3) {
      //sticker
      type = "warning";
    }
    return type;
  }

  return (
    <>
      <Modal
        className="slf-w-full-responsive"
        open={detailOpen}
        centered
        footer={null}
        onCancel={handleCancel}
      >
        <div>
          {selectedPrecondition ? (
            <>
              <div className="row mb-4">
                <div className={`col-12 py-4 text-${getType()}`}>
                  <div className="d-flex">
                    <FaGlobeEurope
                      style={{ fontSize: 24 }}
                      className="me-2"
                    ></FaGlobeEurope>{" "}
                    {t("site:visaInfo")}:{" "}
                    {selectedPrecondition?.visaInfo?.displayValue}
                  </div>
                </div>
                <div className="col-12">
                  <Alert
                    message={t("site:infoNote")}
                    description={selectedPrecondition?.displayMessage}
                    type={getType()}
                    showIcon
                  />
                </div>
              </div>
              <Divider></Divider>
            </>
          ) : null}

          {selectedPrecondition?.visaInfoId == 1 &&
          selectedPrecondition?.isMobileOnly !== true ? (
            <div className="row px-4">
              {questions?.length > 0 ? (
                <div className="col-12 mt-4 mb-4">
                  <h6 className="mb-2">{t("site:questionConditionText")}</h6>
                  <ul>
                    {questions.map((q) => (
                      <li key={q.id}>
                        <BsDot />
                        {q.definition}
                      </li>
                    ))}
                  </ul>
                </div>
              ) : null}
            </div>
          ) : null}
        </div>
      </Modal>
      <Form className="slf-section-1-form" layout={"vertical"} form={form}>
        <div className="slf-section-1-form-row">
          <Form.Item
            className="slf-section-1-form-label"
            label={t("site:nationalityInformation")}
            name={["availability", "nationality"]}
          >
            <Spin indicator={antIcon} spinning={loading}>
              <Select
                showSearch
                size="large"
                style={{ width: "100%" }}
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.label.toLowerCase() ?? "").includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
                options={countriesRef?.current?.map((country) => {
                  return { value: country.id, label: country.name };
                })}
                onChange={onCountryChange}
              />
            </Spin>
          </Form.Item>
        </div>
        <div className="slf-section-1-form-row">
          <Form.Item
            className="slf-section-1-form-label"
            label={t("site:passportType")}
            name={["availability", "passport"]}
          >
            <Select
              showSearch
              size="large"
              style={{ width: "100%" }}
              options={passportTypes?.map((passport) => {
                return { value: passport.id, label: passport.name };
              })}
              onChange={onPassportChange}
            />
          </Form.Item>
        </div>
        <Form.Item className="slf-section-1-form-buttons">
          <Button
            className="slf-section-1-action-button"
            type="primary"
            onClick={checkAvailabilityBtn}
          >
            {t("site:checkAvailability")}
          </Button>
        </Form.Item>
      </Form>
    </>
  );
};

export default CheckAvailability;
