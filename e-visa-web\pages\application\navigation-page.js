import Container from "react-bootstrap/Container";
import useTranslation from "next-translate/useTranslation";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { Button, Form, Input, Modal, DatePicker } from "antd";
import { getDateFormat } from "../../utils/tools";
import dayjs from "dayjs";
import {
  initializeConsts,
  groupMin,
  groupMax,
  familyMax,
} from "../api/regulations";
import Image from "next/image";

const NavigationPage = () => {
  React.useLayoutEffect = React.useEffect;
  const { TextArea } = Input;
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const handleCancelModal = () => setIsOpen(false);
  const [loadingAction, setLoadingAction] = useState(false);
  const [groupMinCount, setGroupMinCount] = useState(0);
  const [groupMaxCount, setGroupMaxCount] = useState(0);
  const [FamilyMaxCount, setFamilyMaxCount] = useState(0);

  useEffect(() => {
    setVariables();
  }, []);

  const setVariables = async () => {
    await initializeConsts();
    setGroupMinCount(groupMin);
    setGroupMaxCount(groupMax);
    setFamilyMaxCount(familyMax);
  };
  const applyIndividual = async () => {
    router.push(
      {
        path: "/application/new-application",
        query: { investigationTypeId: 1 },
      },
      "/application/new-application"
    );
  };

  const applyFamily = async () => {
    router.push(
      {
        path: "/application/new-application",
        query: { investigationTypeId: 2 },
      },
      "/application/new-application"
    );
  };

  const applyGroup = async () => {
    setIsOpen(true);
  };

  const onFinish = async (values) => {
    setLoadingAction(true);

    var model = {
      companyName: values.company.name,
      companyAddress: values.company.address,
      investigationTypeId: 3,
      visaEntryDate: dayjs(values.company.visaEntryDate).format("DD/MM/YYYY"),
    };

    setLoadingAction(false);
    router.push(
      { path: "/application/new-application", query: model },
      "/application/new-application"
    );
  };

  return (
    <>
      <Modal
        open={isOpen}
        title={t("site:companyInfo")}
        footer={null}
        onCancel={handleCancelModal}
      >
        <Form
          form={form}
          name="frm-addCompany"
          labelCol={{
            span: 12,
          }}
          size="large"
          onFinish={onFinish}
          wrapperCol={{
            span: 32,
          }}
          layout="vertical"
        >
          <div className="bg-transparent">
            <div className="">
              <div className="row">
                <div className="col-lg-12 col-md-12 col-sm-12">
                  <Form.Item
                    name={["company", "name"]}
                    label={t("form:companyName")}
                    rules={[
                      {
                        required: true,
                        max: 50,
                      },
                    ]}
                  >
                    <Input maxLength={50} />
                  </Form.Item>
                </div>
              </div>
              <div className="row">
                <div className="col-lg-12 col-md-12 col-sm-12">
                  <Form.Item
                    label={t("form:companyAddress")}
                    name={["company", "address"]}
                    rules={[{ required: true, max: 200 }]}
                  >
                    <TextArea rows={2} />
                  </Form.Item>
                </div>
              </div>
              <div className="row">
                <div className="col-lg-12 col-md-12 col-sm-12">
                  <Form.Item
                    label={t("form:visaEntryDate")}
                    name={["company", "visaEntryDate"]}
                    rules={[{ required: true }]}
                  >
                    <DatePicker
                      format={getDateFormat[router?.locale]}
                      style={{ width: "100%" }}
                      disabledDate={(current) => {
                        return current && current < new Date();
                      }}
                    />
                  </Form.Item>
                </div>
              </div>
            </div>
          </div>

          <div className="row mt-4">
            <div className="col-12 text-center">
              <Button
                size="large"
                htmlType="submit"
                className="btn stepper-button-next"
                loading={loadingAction}
              >
                {t("form:save")}
              </Button>
            </div>
          </div>
        </Form>
      </Modal>

      <Container className="slf-container">
        <h2 className="slf-section-2-4-row-h2 slf-w-full-center slf-mt-48-responsive">
          {t("site:typesOfVisas")}
        </h2>
        <div className="slf-section-2-4-row slf-mt-60-responsive">
          <Button
            className={`slf-btn-visas-individual`}
            type="primary"
            onClick={applyIndividual}
          >
            {t("site:applyIndividual")}
            <span className="slf-btn-visas-desc">
              {t("site:individualDescription")}
            </span>
          </Button>
          <Button
            className={`slf-btn-visas-family`}
            type="primary"
            onClick={applyFamily}
          >
            {t("site:applyFamily")}
            <span className="slf-btn-visas-desc">
              {t("site:familyDescription", { max: FamilyMaxCount })}
            </span>
          </Button>
          <Button
            className={`slf-btn-visas-group`}
            type="primary"
            onClick={applyGroup}
          >
            {t("site:applyGroup")}
            <span className="slf-btn-visas-desc">
              {t("site:groupDescription", {
                min: groupMinCount,
                max: groupMaxCount,
              })}
            </span>
          </Button>
        </div>
        <div className="slf-section-2-4-row mb-5 slf-mt-72-responsive">
          <div>
            <h2 className="slf-section-2-4-row-h2">{t("site:howGetVisa")}</h2>
            <p
              className="slf-section-2-4-row-p"
              dangerouslySetInnerHTML={{
                __html: t("site:howGetVisaDesc"),
              }}
            ></p>
          </div>
          <div>
            <Image
              src="/img/landing/section-2-4-2.png"
              width={635}
              height={431}
              alt="video"
            ></Image>
          </div>
        </div>
      </Container>
    </>
  );
};

export default NavigationPage;
