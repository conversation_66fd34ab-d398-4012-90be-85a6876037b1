export function IconEdit({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="23"
      viewBox="0 0 24 23"
      fill="none"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        d="M9 7.08167H6C5.46957 7.08167 4.96086 7.27169 4.58579 7.60993C4.21071 7.94817 4 8.40692 4 8.88527V17.0015C4 17.4798 4.21071 17.9386 4.58579 18.2768C4.96086 18.6151 5.46957 18.8051 6 18.8051H15C15.5304 18.8051 16.0391 18.6151 16.4142 18.2768C16.7893 17.9386 17 17.4798 17 17.0015V14.2961"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 14.2961H12L20.5 6.63082C20.8978 6.27206 21.1213 5.78548 21.1213 5.27812C21.1213 4.77076 20.8978 4.28418 20.5 3.92542C20.1022 3.56666 19.5626 3.36511 19 3.36511C18.4374 3.36511 17.8978 3.56666 17.5 3.92542L9 11.5907V14.2961Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16 5.27808L19 7.98348"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
export function IconFileText({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="22"
      viewBox="0 0 24 22"
      fill="none"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        d="M14 2.97546V6.58266C14 6.82183 14.1054 7.0512 14.2929 7.22032C14.4804 7.38944 14.7348 7.48446 15 7.48446H19"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17 19.2079H7C6.46957 19.2079 5.96086 19.0178 5.58579 18.6796C5.21071 18.3414 5 17.8826 5 17.4043V4.77906C5 4.30072 5.21071 3.84197 5.58579 3.50373C5.96086 3.16549 6.46957 2.97546 7 2.97546H14L19 7.48446V17.4043C19 17.8826 18.7893 18.3414 18.4142 18.6796C18.0391 19.0178 17.5304 19.2079 17 19.2079Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M9 8.38623H10" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M9 11.9934H15" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M9 15.6007H15" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}
export function IconUser({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="23"
      viewBox="0 0 24 23"
      fill="none"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        d="M12 10.6907C14.2091 10.6907 16 9.07572 16 7.08352C16 5.09132 14.2091 3.47632 12 3.47632C9.79086 3.47632 8 5.09132 8 7.08352C8 9.07572 9.79086 10.6907 12 10.6907Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6 19.7087V17.9051C6 16.9484 6.42143 16.0309 7.17157 15.3544C7.92172 14.678 8.93913 14.2979 10 14.2979H14C15.0609 14.2979 16.0783 14.678 16.8284 15.3544C17.5786 16.0309 18 16.9484 18 17.9051V19.7087"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
export function IconQuestionMark({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="22"
      viewBox="0 0 24 22"
      fill="none"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        d="M8 7.48628C8 6.76876 8.36875 6.08064 9.02513 5.57328C9.6815 5.06592 10.5717 4.78088 11.5 4.78088H12.5C13.4283 4.78088 14.3185 5.06592 14.9749 5.57328C15.6313 6.08064 16 6.76876 16 7.48628C16.0368 8.07178 15.8617 8.65221 15.501 9.14015C15.1402 9.6281 14.6135 9.99713 14 10.1917C13.3865 10.4511 12.8598 10.9431 12.499 11.5937C12.1383 12.2443 11.9632 13.0182 12 13.7989"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 17.4061V17.4161"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
export function IconPhone({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="23"
      viewBox="0 0 24 23"
      fill="none"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        d="M5 4.37939H9L11 8.8884L8.5 10.2411C9.57096 12.1994 11.3285 13.7843 13.5 14.7501L15 12.4956L20 14.2992V17.9064C20 18.3848 19.7893 18.8435 19.4142 19.1818C19.0391 19.52 18.5304 19.71 18 19.71C14.0993 19.4962 10.4202 18.0025 7.65683 15.5105C4.8935 13.0185 3.23705 9.70069 3 6.183C3 5.70465 3.21071 5.2459 3.58579 4.90766C3.96086 4.56942 4.46957 4.37939 5 4.37939Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
export function IconWorld({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="22"
      viewBox="0 0 24 22"
      fill="none"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        d="M3.60001 8.38895H20.4M3.60001 13.7998H20.4M11.5 2.97815C9.81532 5.41266 8.92218 8.2247 8.92218 11.0944C8.92218 13.964 9.81532 16.776 11.5 19.2106M12.5 2.97815C14.1847 5.41266 15.0778 8.2247 15.0778 11.0944C15.0778 13.964 14.1847 16.776 12.5 19.2106M21 11.0944C21 15.5768 16.9706 19.2106 12 19.2106C7.02944 19.2106 3 15.5768 3 11.0944C3 6.6119 7.02944 2.97815 12 2.97815C16.9706 2.97815 21 6.6119 21 11.0944Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
export function IconTablerFlag({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="23"
      viewBox="0 0 24 23"
      fill="none"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        d="M5 5.28308V19.7119"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19 5.28308V13.3993"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5 5.28307C5.93464 4.4569 7.19124 3.99414 8.5 3.99414C9.80876 3.99414 11.0654 4.4569 12 5.28307C12.9346 6.10924 14.1912 6.572 15.5 6.572C16.8088 6.572 18.0654 6.10924 19 5.28307"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5 13.3993C5.93464 12.5731 7.19124 12.1104 8.5 12.1104C9.80876 12.1104 11.0654 12.5731 12 13.3993C12.9346 14.2255 14.1912 14.6882 15.5 14.6882C16.8088 14.6882 18.0654 14.2255 19 13.3993"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
export function IconCheckBox({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="22"
      viewBox="0 0 24 22"
      fill="none"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        d="M9 10.1952L12 12.9006L20 5.68617M20 11.097V16.5078C20 16.9861 19.7893 17.4448 19.4142 17.7831C19.0391 18.1213 18.5304 18.3114 18 18.3114H6C5.46957 18.3114 4.96086 18.1213 4.58579 17.7831C4.21071 17.4448 4 16.9861 4 16.5078V5.68617C4 5.20782 4.21071 4.74907 4.58579 4.41083C4.96086 4.07259 5.46957 3.88257 6 3.88257H15"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
export function IconEntry({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="27"
      viewBox="0 0 28 27"
      fill="none"
      strokeWidth="1.5"
      stroke="currentColor"
    >
      <path
        d="M10.6667 4.38257L19 4.38257C20.3807 4.38257 21.5 5.39194 21.5 6.63707L21.5 20.1641C21.5 21.4092 20.3807 22.4186 19 22.4186L10.6667 22.4186C9.28595 22.4186 8.16667 21.4092 8.16667 20.1641L8.16667 19.2247C8.16667 18.7059 8.63304 18.2853 9.20833 18.2853C9.78363 18.2853 10.25 18.7059 10.25 19.2247L10.25 20.1641C10.25 20.3716 10.4365 20.5398 10.6667 20.5398L19 20.5398C19.2301 20.5398 19.4167 20.3716 19.4167 20.1641L19.4167 6.63707C19.4167 6.42955 19.2301 6.26132 19 6.26132L10.6667 6.26132C10.4365 6.26132 10.25 6.42955 10.25 6.63707L10.25 7.57645C10.25 8.09525 9.78363 8.51583 9.20833 8.51583C8.93207 8.51583 8.66711 8.41686 8.47176 8.24069C8.27641 8.06452 8.16667 7.82559 8.16667 7.57645L8.16667 6.63707C8.16667 5.39194 9.28595 4.38257 10.6667 4.38257ZM12.8958 16.7448L16.0125 13.9304C16.3356 13.6373 16.3356 13.1639 16.0125 12.8708L12.8958 10.0564C12.658 9.84012 12.2988 9.77473 11.9865 9.89084C11.6742 10.007 11.4706 10.2816 11.4708 10.5862L11.4708 11.8976L7.33333 11.8976C6.8731 11.8976 6.5 12.234 6.5 12.6491L6.5 14.1521C6.5 14.5671 6.8731 14.9036 7.33333 14.9036L11.4708 14.9036L11.4708 16.215C11.4706 16.5196 11.6742 16.7942 11.9865 16.9103C12.2988 17.0264 12.658 16.961 12.8958 16.7448Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
export function IconGroup460({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="73"
      height="80"
      viewBox="0 0 73 80"
      fill="none"
    >
      <path
        opacity="0.15"
        d="M30.3559 1.56851C33.9781 -0.522765 38.4408 -0.522765 42.063 1.56851L66.5654 15.715C70.1876 17.8062 72.4189 21.6711 72.4189 25.8536V54.1465C72.4189 58.329 70.1876 62.1939 66.5654 64.2851L42.063 78.4316C38.4408 80.5229 33.9781 80.5229 30.3559 78.4316L5.85356 64.2851C2.23136 62.1939 0 58.329 0 54.1465V25.8536C0 21.6711 2.23136 17.8062 5.85355 15.715L30.3559 1.56851Z"
        fill="#FD4C5C"
      />
    </svg>
  );
}
export function IconGroup460Inside({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
    >
      <path
        d="M27.7255 0.978393L29.7847 4.61767L33.3681 6.61962L36.5502 4.22855L39.406 0.583108C38.9626 0.545571 33.9623 -0.938525 27.7255 0.978393Z"
        fill="#FF6B6B"
      />
      <path
        d="M39.4061 0.583099L33.3682 6.61961L35.2653 10.0968L39.0108 12.2609C40.9501 5.95469 39.447 1.06673 39.4061 0.583099Z"
        fill="#FF3D7D"
      />
      <path
        d="M8.24219 8.29592L13.0029 13.0555L14.1191 12.1934L14.5031 11.2287L12.9155 8.06098L9.90592 6.6326L8.24219 8.29592Z"
        fill="#FF3D7D"
      />
      <path
        d="M19.8053 5.35409L12.9094 3.63047L9.90637 6.63264L14.5036 11.2288L17.095 9.89521L19.8053 5.35409Z"
        fill="#FF6B6B"
      />
      <path
        d="M31.7178 31.7653L33.3815 30.1021L31.5413 27.472L28.7663 25.488L27.5052 26.0013L26.9572 27.0058L31.7178 31.7653Z"
        fill="#C1126B"
      />
      <path
        d="M36.3844 27.0997L34.6604 20.2054L30.2019 22.9989L28.7662 25.4879L33.3814 30.1021L36.3844 27.0997Z"
        fill="#FF3D7D"
      />
      <path
        d="M10.395 21.2313L12.0741 19.5526L7.6604 23.9653L11.8523 28.1563L14.0042 27.1226L14.5871 25.4222L13.3751 22.63L10.395 21.2313Z"
        fill="#613D5C"
      />
      <path
        d="M14.921 20.8974L12.0742 19.5526L10.3951 21.2313L14.5872 25.4222L15.4823 25.1227L16.2661 23.7437L14.921 20.8974Z"
        fill="#FF6B6B"
      />
      <path
        d="M11.8518 28.1567L16.0438 32.3478L18.7788 29.6134L17.2813 26.5357L14.5869 25.4225L11.8518 28.1567Z"
        fill="#4B2746"
      />
      <path
        d="M19.29 25.2655L16.2661 23.7437L14.5869 25.4226L18.7788 29.6135L20.458 27.9346L19.29 25.2655Z"
        fill="#FF3D7D"
      />
      <path
        d="M27.7255 0.978363C23.4601 2.28971 19.6864 4.85037 16.8918 8.33918L9.62024 17.0994L16.2529 23.7306L26.1214 15.4451L33.3681 6.61951L27.7255 0.978363Z"
        fill="#F2F2FC"
      />
      <path
        d="M16.2533 23.7305L22.8862 30.3617L31.6485 23.092C35.1319 20.3033 37.697 16.5333 39.0112 12.2608L33.3685 6.6195L16.2533 23.7305Z"
        fill="#E2D9FB"
      />
      <path
        d="M0 31.6179L5.97902 25.6388L7.6553 27.3151L1.67628 33.2941L0 31.6179Z"
        fill="#E2D9FB"
      />
      <path
        d="M6.70508 38.324L12.6848 32.3457L14.3613 34.0218L8.3816 40.0001L6.70508 38.324Z"
        fill="#C7C1DC"
      />
      <path
        d="M9.33627 28.9951L1.67883 36.6507L2.51718 37.4889L6.83704 34.0083L10.1747 29.8332L9.33627 28.9951Z"
        fill="#E2D9FB"
      />
      <path
        d="M2.51404 37.484L10.1695 29.8286L11.0076 30.6667L3.35215 38.3222L2.51404 37.484Z"
        fill="#C7C1DC"
      />
      <path
        d="M21.6308 11.6487C19.7774 13.5017 19.7771 16.5009 21.6307 18.3542L25.9834 15.5831L28.3379 11.6487C26.4843 9.79588 23.4846 9.79541 21.6308 11.6487Z"
        fill="#613D5C"
      />
      <path
        d="M28.3379 11.6487L21.6306 18.3542L21.6307 18.3543C23.484 20.2071 26.484 20.2076 28.3379 18.3543C30.187 16.5055 30.187 13.4974 28.3379 11.6487Z"
        fill="#613D5C"
      />
      <path
        d="M23.3074 13.3252C22.3806 14.2517 22.3806 15.7512 23.3074 16.6779L25.7461 15.8202L26.6609 13.3251C25.7339 12.3985 24.2342 12.3985 23.3074 13.3252Z"
        fill="#7ED8F6"
      />
      <path
        d="M26.6614 13.3251L23.3077 16.6779H23.3078C24.2344 17.6044 25.7344 17.6047 26.6614 16.6779V16.678C27.5859 15.7536 27.5859 14.2496 26.6614 13.3252L26.6614 13.3251Z"
        fill="#6AA9FF"
      />
    </svg>
  );
}
export function IconGroup457({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="73"
      height="80"
      viewBox="0 0 73 80"
      fill="none"
    >
      <path
        opacity="0.15"
        d="M30.3559 1.56846C33.9781 -0.522818 38.4408 -0.522819 42.063 1.56845L66.5654 15.7149C70.1876 17.8062 72.4189 21.671 72.4189 25.8536V54.1465C72.4189 58.329 70.1876 62.1938 66.5654 64.2851L42.063 78.4315C38.4408 80.5228 33.9781 80.5228 30.3559 78.4315L5.85356 64.2851C2.23136 62.1938 0 58.329 0 54.1465V25.8536C0 21.671 2.23136 17.8062 5.85355 15.7149L30.3559 1.56846Z"
        fill="#2B59FF"
      />
    </svg>
  );
}
export function IconGroup457Inside({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="43"
      height="43"
      viewBox="0 0 43 43"
      fill="none"
    >
      <path
        d="M32.8123 22.7647H26.3986L25.5554 21.5L26.3986 20.2353H32.8123L33.6554 21.4578L32.8123 22.7647Z"
        fill="#FF3D7D"
      />
      <path
        d="M10.1876 22.7647H16.6014L17.4445 21.5562L16.6014 20.2353H10.1876L9.34448 21.6406L10.1876 22.7647Z"
        fill="#FF6B6B"
      />
      <path
        d="M17.7058 5.5007V3.79419C17.7058 1.70228 19.4081 7.62939e-05 21.4999 7.62939e-05L22.3431 2.75039L21.4999 5.05889L19.6847 6.1134L17.7058 5.5007Z"
        fill="#FF6B6B"
      />
      <path
        d="M21.5 5.05889V7.62939e-05C23.6047 7.62939e-05 25.2941 1.7054 25.2941 3.79419V5.5007L23.2478 6.10556L21.5 5.05889Z"
        fill="#FF3D7D"
      />
      <path
        d="M21.4999 37.9412L22.3431 40.2497L21.4999 43.0001C19.4081 43.0001 17.7058 41.2978 17.7058 39.2059V37.4994L19.8968 36.9114L21.4999 37.9412Z"
        fill="#FF6B6B"
      />
      <path
        d="M21.5 37.9412V43C23.5972 43 25.2941 41.303 25.2941 39.2059V37.4994L23.0999 36.9118L21.5 37.9412Z"
        fill="#FF3D7D"
      />
      <path
        d="M30.1261 7.50818L31.3368 6.29744C32.8194 4.81487 35.2195 4.81436 36.7026 6.29744C37.0609 6.65577 37.3392 7.07228 37.5271 7.52766C38.0883 8.8835 37.8351 10.5306 36.7026 11.6632L35.4919 12.8739L32.8822 11.1074L30.1261 7.50818Z"
        fill="#FF3D7D"
      />
      <path
        d="M12.8738 35.4919L11.6631 36.7027C10.1787 38.1872 7.77648 38.1836 6.29736 36.7027C4.81462 35.2199 4.81429 32.8199 6.29736 31.3369L7.50811 30.1262L10.1178 31.8927L12.8738 35.4919Z"
        fill="#FF6B6B"
      />
      <path
        d="M37.4993 25.2942H39.2058C41.303 25.2942 42.9999 23.5972 42.9999 21.5001C42.9999 19.4082 41.2976 17.706 39.2058 17.706H37.4993L36.6561 21.753L37.4993 25.2942Z"
        fill="#FF3D7D"
      />
      <path
        d="M5.50062 25.2942H3.79411C1.6969 25.2942 0 23.5972 0 21.5001C0 19.4082 1.70229 17.706 3.79411 17.706H5.50062L6.34376 21.5001L5.50062 25.2942Z"
        fill="#FF6B6B"
      />
      <path
        d="M35.4919 30.1262L36.7026 31.3369C38.1853 32.8196 38.1856 35.2196 36.7026 36.7027C35.2248 38.1823 32.8227 38.1885 31.3368 36.7027L30.1261 35.4919L33.083 31.6919L35.4919 30.1262Z"
        fill="#FF3D7D"
      />
      <path
        d="M7.50811 12.8739L6.29736 11.6632C4.81462 10.1805 4.81429 7.78047 6.29736 6.29739C7.77622 4.81777 10.1834 4.81777 11.6631 6.29739L12.8738 7.50814L10.2643 10.9609L7.50811 12.8739Z"
        fill="#FF6B6B"
      />
      <path
        d="M21.4999 10.1177L22.3431 13.3371L21.4999 16.4412L20.938 17.3556L20.2352 16.6014V10.1877L20.8676 9.30956L21.4999 10.1177Z"
        fill="#FF6B6B"
      />
      <path
        d="M21.5 10.1177V16.4412L22.1324 17.3645L22.7647 16.6014V10.1877L22.1324 9.30956L21.5 10.1177Z"
        fill="#FF3D7D"
      />
      <path
        d="M21.4999 32.8824L22.3431 30.0445L21.4999 26.5588L20.8726 25.6363L20.2352 26.3986V32.8124L20.8676 33.6905L21.4999 32.8824Z"
        fill="#FF6B6B"
      />
      <path
        d="M21.5 32.8824V26.5589L22.1324 25.6357L22.7647 26.3987V32.8124L21.9623 33.6999L21.5 32.8824Z"
        fill="#FF3D7D"
      />
      <path
        d="M10.1177 21.5001C10.1177 15.2238 15.2237 10.1177 21.5 10.1177L22.3431 7.25106L21.5 5.05891C12.4343 5.05891 5.05884 12.4344 5.05884 21.5001C5.05884 30.5657 12.4343 37.9412 21.5 37.9412L22.3431 35.2019L21.5 32.8824C15.2237 32.8824 10.1177 27.7763 10.1177 21.5001Z"
        fill="#FEE05B"
      />
      <path
        d="M21.5 5.05891V10.1177C27.7762 10.1177 32.8823 15.2238 32.8823 21.5001C32.8823 27.7763 27.7762 32.8824 21.5 32.8824V37.9412C30.5657 37.9412 37.9412 30.5657 37.9412 21.5001C37.9412 12.4344 30.5657 5.05891 21.5 5.05891Z"
        fill="#FEC258"
      />
      <path
        d="M16.4412 21.5C16.4412 24.2895 18.7105 26.5589 21.5 26.5589L22.3431 21.5007L21.5 16.4412C18.7105 16.4412 16.4412 18.7106 16.4412 21.5Z"
        fill="#FEE05B"
      />
      <path
        d="M21.5 16.4412V26.5589C24.2894 26.5589 26.5588 24.2895 26.5588 21.5C26.5588 18.7106 24.2894 16.4412 21.5 16.4412Z"
        fill="#FEC258"
      />
    </svg>
  );
}
export function IconGroup458({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="73"
      height="80"
      viewBox="0 0 73 80"
      fill="none"
    >
      <path
        opacity="0.15"
        d="M30.3559 1.56846C33.9781 -0.522818 38.4408 -0.522819 42.063 1.56845L66.5654 15.7149C70.1876 17.8062 72.4189 21.671 72.4189 25.8536V54.1465C72.4189 58.329 70.1876 62.1938 66.5654 64.2851L42.063 78.4315C38.4408 80.5228 33.9781 80.5228 30.3559 78.4315L5.85356 64.2851C2.23136 62.1938 0 58.329 0 54.1465V25.8536C0 21.671 2.23136 17.8062 5.85355 15.7149L30.3559 1.56846Z"
        fill="#00BB98"
      />
    </svg>
  );
}
export function IconGroup458Inside({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="45"
      height="45"
      viewBox="0 0 45 45"
      fill="none"
    >
      <path
        d="M21.1761 21.1764V31.1286L16.3108 35.0208L16.4989 36.4725L17.7331 37.273L22.4996 33.4596L23.382 27.6765L22.4996 21.1765L21.8599 20.2941L21.1761 21.1764Z"
        fill="#F2F2FC"
      />
      <path
        d="M23.8235 31.1286V21.1764L23.2279 20.2941L22.5 21.1765V33.4596L27.2665 37.2728L28.729 36.5914L28.6887 35.0207L23.8235 31.1286Z"
        fill="#E2D9FB"
      />
      <path
        d="M18.5564 8.82279C15.4142 10.3067 13.2349 13.5066 13.2349 17.2058V18.5294L17.7128 19.4779L22.4996 18.5294L23.3819 15.8823L22.4996 10.5882L20.5136 8.81662L18.5564 8.82279Z"
        fill="#FEE05B"
      />
      <path
        d="M22.5 10.5882V18.5294L26.7794 19.2573L31.7647 18.5294V17.2058C31.7647 13.5075 29.5864 10.3081 26.4453 8.82375L24.5548 8.78687L22.5 10.5882Z"
        fill="#FEC258"
      />
      <path
        d="M17.2058 5.29412C17.2058 8.21329 19.5808 10.5882 22.4999 10.5882L23.8235 5.29412L22.4999 0C19.5808 0 17.2058 2.37494 17.2058 5.29412Z"
        fill="#FECEC1"
      />
      <path
        d="M22.5 0V10.5882C25.4192 10.5882 27.7941 8.21329 27.7941 5.29412C27.7941 2.37494 25.4192 0 22.5 0Z"
        fill="#F7AD9E"
      />
      <path
        d="M5.32165 32.6463C2.17932 34.1303 0 37.3301 0 41.0294V42.353L5.07353 43.3897L9.26471 42.353L10.1471 39.7059L9.26471 34.4118L7.27871 32.6403L5.32165 32.6463Z"
        fill="#BEE75E"
      />
      <path
        d="M9.26514 34.4118V42.3529L13.2357 43.2794L18.5298 42.3529V41.0294C18.5298 37.3309 16.3515 34.1317 13.2104 32.6473L11.32 32.6104L9.26514 34.4118Z"
        fill="#5BC980"
      />
      <path
        d="M3.97046 29.1176C3.97046 32.0368 6.3454 34.4117 9.26458 34.4117L10.5881 29.1176L9.26458 23.8235C6.3454 23.8235 3.97046 26.1984 3.97046 29.1176Z"
        fill="#FEDFD0"
      />
      <path
        d="M9.26514 23.8235V34.4117C12.1843 34.4117 14.5593 32.0368 14.5593 29.1176C14.5593 26.1984 12.1843 23.8235 9.26514 23.8235Z"
        fill="#FECEC1"
      />
      <path
        d="M31.7923 32.6463C28.65 34.1303 26.4707 37.3301 26.4707 41.0294V42.353L31.7354 43.1177L35.7354 42.353L36.6178 39.7059L35.7354 34.4118L33.7494 32.6403L31.7923 32.6463Z"
        fill="#7ED8F6"
      />
      <path
        d="M35.7349 34.4118V42.3529L39.7496 43.2794L44.9996 42.3529V41.0294C44.9996 37.3309 42.8212 34.1317 39.6801 32.6473L37.7897 32.6104L35.7349 34.4118Z"
        fill="#6AA9FF"
      />
      <path
        d="M30.4414 29.1176C30.4414 32.0368 32.8163 34.4117 35.7355 34.4117L37.059 29.1176L35.7355 23.8235C32.8163 23.8235 30.4414 26.1984 30.4414 29.1176Z"
        fill="#FEDFD0"
      />
      <path
        d="M35.7349 23.8235V34.4117C38.654 34.4117 41.029 32.0368 41.029 29.1176C41.029 26.1984 38.654 23.8235 35.7349 23.8235Z"
        fill="#FECEC1"
      />
      <path
        d="M0 42.3529V45H9.26471L10.1471 43.6765L9.26471 42.3529H0Z"
        fill="#5BC980"
      />
      <path d="M9.26514 42.3529H18.5298V45H9.26514V42.3529Z" fill="#00A78E" />
      <path
        d="M26.4707 42.3529V45H35.7354L36.6178 43.6765L35.7354 42.3529H26.4707Z"
        fill="#6AA9FF"
      />
      <path d="M35.7349 42.3529H44.9996V45H35.7349V42.3529Z" fill="#4987EA" />
      <path
        d="M13.2349 18.5294V21.1765H22.4996L23.3819 19.853L22.4996 18.5294H13.2349Z"
        fill="#FEC258"
      />
      <path d="M22.5 18.5294H31.7647V21.1765H22.5V18.5294Z" fill="#FD996B" />
    </svg>
  );
}
export function IconMap({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="21"
      viewBox="0 0 24 21"
      fill="none"
      stroke="#86869B"
      strokeWidth="1.5"
    >
      <path d="M7.06961 5.61841C5.4279 6.04344 3.67612 7.08069 2.72215 7.7017C2.25538 8.00556 2 8.48485 2 8.98866V17.3134C2 18.0482 3.0279 18.4743 3.73627 18.0644C4.94575 17.3647 6.58869 16.5701 8 16.4341C11.3183 16.1143 12.6817 19.5004 16 19.1806C17.8673 19.0006 20.14 17.6678 21.2778 16.9271C21.7446 16.6233 22 16.144 22 15.6402V7.31541C22 6.58064 20.9721 6.15456 20.2637 6.56438C19.2185 7.16909 17.8496 7.84462 16.5862 8.10474" />
      <path d="M17 6.11565C17 8.39796 14.5 12.3144 12 12.3144C9.5 12.3144 7 8.39796 7 6.11565C7 3.83333 9.23858 1.98315 12 1.98315C14.7614 1.98315 17 3.83333 17 6.11565Z" />
      <path d="M13.5 5.85737C13.5 6.57059 12.8284 7.14877 12 7.14877C11.1716 7.14877 10.5 6.57059 10.5 5.85737C10.5 5.14414 11.1716 4.56596 12 4.56596C12.8284 4.56596 13.5 5.14414 13.5 5.85737Z" />
    </svg>
  );
}
export function IconRedArrowRight({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="8"
      viewBox="0 0 16 8"
      fill="none"
    >
      <path
        d="M15.3536 4.3527C15.5488 4.15744 15.5488 3.84085 15.3536 3.64559L12.1716 0.463612C11.9763 0.268349 11.6597 0.268349 11.4645 0.463612C11.2692 0.658874 11.2692 0.975456 11.4645 1.17072L14.2929 3.99915L11.4645 6.82757C11.2692 7.02283 11.2692 7.33942 11.4645 7.53468C11.6597 7.72994 11.9763 7.72994 12.1716 7.53468L15.3536 4.3527ZM0 4.49915H15V3.49915H0V4.49915Z"
        fill="#FD4C5C"
      />
    </svg>
  );
}
export function IconSupport({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="52"
      height="52"
      viewBox="0 0 52 52"
      fill="none"
    >
      <path
        d="M23.8334 45.5V42.25H42.25V25.7833C42.25 23.6889 41.7987 21.6757 40.8959 19.7438C39.9931 17.8118 38.7834 16.1056 37.2667 14.625C35.75 13.1444 34.0167 11.9618 32.0667 11.0771C30.1167 10.1924 28.0945 9.75 26 9.75C23.9056 9.75 21.8834 10.1924 19.9334 11.0771C17.9834 11.9618 16.25 13.1444 14.7334 14.625C13.2167 16.1056 12.007 17.8118 11.1042 19.7438C10.2014 21.6757 9.75004 23.6889 9.75004 25.7833V39H8.66671C7.47504 39 6.4549 38.5757 5.60629 37.7271C4.75768 36.8785 4.33337 35.8583 4.33337 34.6667V30.3333C4.33337 29.5028 4.53199 28.7715 4.92921 28.1396C5.32643 27.5076 5.85004 26.9931 6.50004 26.5958L6.66254 23.725C6.98754 21.0889 7.73685 18.7056 8.91046 16.575C10.0841 14.4444 11.5466 12.6389 13.298 11.1583C15.0493 9.67778 17.0174 8.53125 19.2021 7.71875C21.3868 6.90625 23.6528 6.5 26 6.5C28.3834 6.5 30.6674 6.90625 32.8521 7.71875C35.0368 8.53125 36.9959 9.68681 38.7292 11.1854C40.4625 12.684 41.916 14.4896 43.0896 16.6021C44.2632 18.7146 45.0125 21.0708 45.3375 23.6708L45.5 26.4875C46.15 26.8125 46.6737 27.291 47.0709 27.9229C47.4681 28.5549 47.6667 29.25 47.6667 30.0083V34.9917C47.6667 35.7861 47.4681 36.4903 47.0709 37.1042C46.6737 37.7181 46.15 38.1875 45.5 38.5125V42.25C45.5 43.1438 45.1818 43.9089 44.5454 44.5453C43.9089 45.1818 43.1438 45.5 42.25 45.5H23.8334ZM19.5 29.7917C19.0667 29.7917 18.6875 29.6292 18.3625 29.3042C18.0375 28.9792 17.875 28.591 17.875 28.1396C17.875 27.6882 18.0375 27.309 18.3625 27.0021C18.6875 26.6951 19.0757 26.5417 19.5271 26.5417C19.9785 26.5417 20.3577 26.6974 20.6646 27.0089C20.9716 27.3203 21.125 27.7063 21.125 28.1667C21.125 28.6 20.9693 28.9792 20.6579 29.3042C20.3464 29.6292 19.9605 29.7917 19.5 29.7917ZM32.5 29.7917C32.0667 29.7917 31.6875 29.6292 31.3625 29.3042C31.0375 28.9792 30.875 28.591 30.875 28.1396C30.875 27.6882 31.0375 27.309 31.3625 27.0021C31.6875 26.6951 32.0757 26.5417 32.5271 26.5417C32.9785 26.5417 33.3577 26.6974 33.6646 27.0089C33.9716 27.3203 34.125 27.7063 34.125 28.1667C34.125 28.6 33.9693 28.9792 33.6579 29.3042C33.3464 29.6292 32.9605 29.7917 32.5 29.7917ZM13.0542 26.975C12.9098 24.8444 13.2077 22.9125 13.948 21.1792C14.6882 19.4458 15.6813 17.9743 16.9271 16.7646C18.173 15.5549 19.6084 14.625 21.2334 13.975C22.8584 13.325 24.4834 13 26.1084 13C29.3945 13 32.157 14.0382 34.3959 16.1146C36.6348 18.191 38.007 20.7819 38.5125 23.8875C35.1181 23.8514 32.1299 22.9396 29.548 21.1521C26.966 19.3646 24.9709 17.0444 23.5625 14.1917C22.9848 17.1167 21.766 19.7076 19.9063 21.9646C18.0466 24.2215 15.7625 25.8917 13.0542 26.975Z"
        fill="#000B33"
      />
    </svg>
  );
}
export function IconFiles({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="27"
      viewBox="0 0 24 27"
      fill="none"
    >
      <path
        d="M3 20.2682H2.25H3ZM3 7.20461H3.75H3ZM7 2.8501V2.1001V2.8501ZM17 2.8501V2.1001V2.8501ZM21 7.20461H20.25H21ZM21 14.1099H21.75H21ZM11.3431 24.6227V23.8727V24.6227ZM7 24.6227V25.3727V24.6227ZM19.8284 17.1891L19.2761 16.6817V16.6817L19.8284 17.1891ZM14.1716 23.3473L14.7239 23.8546L14.1716 23.3473ZM13 20.2682H12.25H13ZM17 15.9136V15.1636V15.1636V15.9136ZM8 7.54324C7.58579 7.54324 7.25 7.87903 7.25 8.29324C7.25 8.70746 7.58579 9.04324 8 9.04324V7.54324ZM16 9.04324C16.4142 9.04324 16.75 8.70746 16.75 8.29324C16.75 7.87903 16.4142 7.54324 16 7.54324V9.04324ZM8 12.9864C7.58579 12.9864 7.25 13.3222 7.25 13.7364C7.25 14.1506 7.58579 14.4864 8 14.4864V12.9864ZM12 14.4864C12.4142 14.4864 12.75 14.1506 12.75 13.7364C12.75 13.3222 12.4142 12.9864 12 12.9864V14.4864ZM3.75 20.2682L3.75 7.20461H2.25L2.25 20.2682H3.75ZM7 3.6001L17 3.6001V2.1001L7 2.1001V3.6001ZM20.25 7.20461V14.1099H21.75V7.20461H20.25ZM11.3431 23.8727H7V25.3727H11.3431V23.8727ZM19.2761 16.6817L13.6192 22.8399L14.7239 23.8546L20.3808 17.6964L19.2761 16.6817ZM11.3431 25.3727C12.6245 25.3727 13.839 24.818 14.7239 23.8546L13.6192 22.8399C13.0039 23.5098 12.1836 23.8727 11.3431 23.8727V25.3727ZM20.25 14.1099C20.25 15.0855 19.8934 16.0096 19.2761 16.6817L20.3808 17.6964C21.2637 16.7352 21.75 15.4442 21.75 14.1099H20.25ZM3.75 7.20461C3.75 5.15267 5.26376 3.6001 7 3.6001V2.1001C4.31796 2.1001 2.25 4.4467 2.25 7.20461H3.75ZM2.25 20.2682C2.25 23.0261 4.31796 25.3727 7 25.3727V23.8727C5.26376 23.8727 3.75 22.3201 3.75 20.2682H2.25ZM17 3.6001C18.7362 3.6001 20.25 5.15267 20.25 7.20461H21.75C21.75 4.4467 19.682 2.1001 17 2.1001V3.6001ZM13.75 24.6227V20.2682H12.25V24.6227H13.75ZM17 16.6636L21 16.6636V15.1636L17 15.1636V16.6636ZM13.75 20.2682C13.75 18.2162 15.2638 16.6636 17 16.6636V15.1636C14.318 15.1636 12.25 17.5102 12.25 20.2682H13.75ZM8 9.04324H16V7.54324H8V9.04324ZM8 14.4864H12V12.9864H8V14.4864Z"
        fill="#86869B"
      />
    </svg>
  );
}
export function IconSocials({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="160"
      height="14"
      viewBox="0 0 160 14"
      fill="none"
    >
      <path
        d="M107.286 0.0158638L105.608 0.0131836C103.722 0.0131836 102.503 1.25747 102.503 3.18334V4.645H100.815C100.669 4.645 100.551 4.76266 100.551 4.90779V7.02556C100.551 7.1707 100.669 7.28822 100.815 7.28822H102.503V12.632C102.503 12.7772 102.621 12.8947 102.767 12.8947H104.969C105.115 12.8947 105.233 12.777 105.233 12.632V7.28822H107.207C107.352 7.28822 107.471 7.1707 107.471 7.02556L107.471 4.90779C107.471 4.83811 107.444 4.77137 107.394 4.72205C107.345 4.67274 107.277 4.645 107.207 4.645H105.233V3.40593C105.233 2.81039 105.376 2.50806 106.155 2.50806L107.286 2.50766C107.432 2.50766 107.55 2.39 107.55 2.245V0.278525C107.55 0.133659 107.432 0.0161318 107.286 0.0158638Z"
        fill="#000B33"
      />
      <path
        d="M160 2.50183C159.482 2.72812 158.929 2.87812 158.353 2.95095C158.946 2.59894 159.398 2.04578 159.611 1.37903C159.058 1.70677 158.448 1.93827 157.798 2.06745C157.274 1.51169 156.526 1.16748 155.71 1.16748C154.128 1.16748 152.854 2.44547 152.854 4.01219C152.854 4.23761 152.873 4.45437 152.92 4.66072C150.544 4.54541 148.442 3.41221 147.029 1.68596C146.783 2.11167 146.638 2.59894 146.638 3.12349C146.638 4.10843 147.148 4.98152 147.908 5.487C147.448 5.47833 146.998 5.34567 146.616 5.13672C146.616 5.14539 146.616 5.15666 146.616 5.16793C146.616 6.54997 147.607 7.69791 148.906 7.96235C148.673 8.02565 148.42 8.05599 148.157 8.05599C147.974 8.05599 147.789 8.04559 147.616 8.00744C147.986 9.1337 149.037 9.96171 150.286 9.98859C149.314 10.7455 148.079 11.2016 146.743 11.2016C146.508 11.2016 146.283 11.1912 146.059 11.1625C147.325 11.9749 148.825 12.4388 150.443 12.4388C155.703 12.4388 158.578 8.10368 158.578 4.34599C158.578 4.22027 158.574 4.09889 158.568 3.97837C159.135 3.57781 159.611 3.07754 160 2.50183Z"
        fill="#2B59FF"
      />
      <path
        d="M62.0379 12.4259V12.4254H62.0407V8.29209C62.0407 6.27003 61.6033 4.7124 59.2276 4.7124C58.0855 4.7124 57.3191 5.33602 57.0062 5.92723H56.9732V4.90118H54.7207V12.4254H57.0662V8.69969C57.0662 7.71872 57.2531 6.77014 58.4739 6.77014C59.6769 6.77014 59.6948 7.88965 59.6948 8.76261V12.4259H62.0379Z"
        fill="#000B33"
      />
      <path
        d="M50.9011 4.91479H53.2494V12.439H50.9011V4.91479Z"
        fill="#000B33"
      />
      <path
        d="M52.0815 1.16846C51.3307 1.16846 50.7214 1.7747 50.7214 2.52181C50.7214 3.26893 51.3307 3.88785 52.0815 3.88785C52.8324 3.88785 53.4416 3.26893 53.4416 2.52181C53.4411 1.7747 52.8319 1.16846 52.0815 1.16846V1.16846Z"
        fill="#000B33"
      />
      <path
        d="M8.3934 1.16748H3.81518C1.70844 1.16748 0 2.86746 0 4.96378V9.51933C0 11.6157 1.70844 13.3156 3.81518 13.3156H8.3934C10.5001 13.3156 12.2086 11.6157 12.2086 9.51933V4.96378C12.2086 2.86746 10.5001 1.16748 8.3934 1.16748ZM11.064 9.51933C11.064 10.9847 9.86606 12.1767 8.3934 12.1767H3.81518C2.34252 12.1767 1.14455 10.9847 1.14455 9.51933V4.96378C1.14455 3.49841 2.34252 2.30637 3.81518 2.30637H8.3934C9.86606 2.30637 11.064 3.49841 11.064 4.96378V9.51933Z"
        fill="#000B33"
      />
      <path
        d="M6.1105 4.21143C4.42495 4.21143 3.05835 5.57126 3.05835 7.24846C3.05835 8.92567 4.42495 10.2855 6.1105 10.2855C7.79604 10.2855 9.16264 8.92567 9.16264 7.24846C9.16264 5.57126 7.79604 4.21143 6.1105 4.21143ZM6.1105 9.14661C5.05903 9.14661 4.2029 8.29472 4.2029 7.24846C4.2029 6.20144 5.05903 5.35032 6.1105 5.35032C7.16196 5.35032 8.01809 6.20144 8.01809 7.24846C8.01809 8.29472 7.16196 9.14661 6.1105 9.14661Z"
        fill="#000B33"
      />
      <path
        d="M9.37697 4.39102C9.60216 4.39102 9.7847 4.20937 9.7847 3.9853C9.7847 3.76123 9.60216 3.57959 9.37697 3.57959C9.15179 3.57959 8.96924 3.76123 8.96924 3.9853C8.96924 4.20937 9.15179 4.39102 9.37697 4.39102Z"
        fill="#000B33"
      />
    </svg>
  );
}

export function IconMastercard({ className }) {
  return (
    <svg
      className={className}
      width="75px"
      height="45px"
      viewBox="0 0 200 120"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <title>mastercard</title>
      <g
        id="Rounded"
        stroke="none"
        strokeWidth="1"
        fill="none"
        fillRule="evenodd"
      >
        <g id="Large" transform="translate(-32.000000, -404.000000)">
          <g id="large/mastercard" transform="translate(32.000000, 404.000000)">
            <rect
              id="Background"
              fill="#10427A"
              x="0"
              y="0"
              width="200"
              height="120"
              rx="8"
            ></rect>
            <g
              id="Logo"
              transform="translate(52.000000, 20.000000)"
              fillRule="nonzero"
            >
              <g id="mastercard">
                <rect
                  id="Rectangle-path"
                  fill="#FF5F00"
                  x="36.444549"
                  y="6.79534022"
                  width="27.0118354"
                  height="49.9501544"
                ></rect>
                <path
                  d="M38.1595777,31.7704094 C38.1595777,21.621526 42.7901781,12.6199079 49.9075824,6.79533142 C44.6767188,2.55927578 38.0738258,-7.82864208e-06 30.8706697,-7.82864208e-06 C13.8060499,-7.82864208e-06 6.74147421e-07,14.2084287 6.74147421e-07,31.7704094 C6.74147421e-07,49.3323899 13.8060499,63.5408265 30.8706697,63.5408265 C38.0738258,63.5408265 44.6767188,60.9815429 49.9075824,56.7454874 C42.7901781,51.0091618 38.1595777,41.9192929 38.1595777,31.7704094 Z"
                  id="Shape"
                  fill="#EB001B"
                ></path>
                <path
                  d="M99.900916,31.7704094 C99.900916,49.3323899 86.0948668,63.5408265 69.0302469,63.5408265 C61.8270906,63.5408265 55.2241973,60.9815429 49.9933344,56.7454874 C57.1964902,50.920911 61.7413387,41.9192929 61.7413387,31.7704094 C61.7413387,21.621526 57.1107383,12.6199079 49.9933344,6.79533142 C55.2241973,2.55927578 61.8270906,-7.82864208e-06 69.0302469,-7.82864208e-06 C86.0948668,-7.82864208e-06 99.900916,14.2966799 99.900916,31.7704094 Z"
                  id="Shape"
                  fill="#F79E1B"
                ></path>
              </g>
              <path
                d="M14.5714286,83.8235292 L14.5714286,78.5294113 C14.5714286,76.4999997 13.2380952,75.1764701 10.9523809,75.1764701 C9.80952379,75.1764701 8.57142853,75.5294112 7.71428569,76.6764701 C7.04761902,75.705882 6.09523806,75.1764701 4.66666666,75.1764701 C3.71428569,75.1764701 2.76190473,75.4411763 2,76.4117648 L2,75.3529409 L0,75.3529409 L0,83.8235292 L2,83.8235292 L2,79.1470587 C2,77.6470586 2.85714284,76.9411763 4.19047618,76.9411763 C5.52380951,76.9411763 6.19047617,77.7352936 6.19047617,79.1470587 L6.19047617,83.8235292 L8.19047617,83.8235292 L8.19047617,79.1470587 C8.19047617,77.6470586 9.14285713,76.9411763 10.3809523,76.9411763 C11.7142857,76.9411763 12.3809524,77.7352936 12.3809524,79.1470587 L12.3809524,83.8235292 L14.5714286,83.8235292 L14.5714286,83.8235292 Z M44.1904762,75.3529409 L40.9523808,75.3529409 L40.9523808,72.7941173 L38.9523812,72.7941173 L38.9523812,75.3529409 L37.1428572,75.3529409 L37.1428572,77.0294113 L38.9523812,77.0294113 L38.9523812,80.9117645 C38.9523812,82.8529411 39.8095237,84 42.0952384,84 C42.9523809,84 43.904762,83.7352938 44.5714285,83.3823527 L44.0000002,81.7941173 C43.4285714,82.1470584 42.7619049,82.2352938 42.2857143,82.2352938 C41.3333336,82.2352938 40.9523808,81.7058822 40.9523808,80.8235291 L40.9523808,77.0294113 L44.1904762,77.0294113 L44.1904762,75.3529409 L44.1904762,75.3529409 Z M61.1428572,75.1764701 C60.0000001,75.1764701 59.2380954,75.705882 58.7619047,76.4117648 L58.7619047,75.3529409 L56.7619047,75.3529409 L56.7619047,83.8235292 L58.7619047,83.8235292 L58.7619047,79.0588233 C58.7619047,77.6470586 59.4285713,76.8529409 60.6666666,76.8529409 C61.047619,76.8529409 61.5238096,76.9411763 61.9047619,77.0294113 L62.4761907,75.2647055 C62.0952383,75.1764701 61.5238096,75.1764701 61.1428572,75.1764701 L61.1428572,75.1764701 L61.1428572,75.1764701 Z M35.5238095,76.0588232 C34.5714286,75.4411763 33.2380953,75.1764701 31.8095238,75.1764701 C29.5238096,75.1764701 27.9999999,76.235294 27.9999999,77.9117644 C27.9999999,79.3235295 29.1428572,80.1176468 31.142857,80.382353 L32.0952382,80.470588 C33.1428571,80.6470583 33.7142856,80.9117645 33.7142856,81.3529411 C33.7142856,81.970588 32.9523809,82.4117646 31.6190475,82.4117646 C30.2857143,82.4117646 29.2380952,81.970588 28.5714284,81.5294114 L27.6190475,82.9411765 C28.6666666,83.6470584 30.0952381,84 31.5238094,84 C34.1904762,84 35.7142857,82.8529411 35.7142857,81.2647057 C35.7142857,79.7647056 34.4761904,78.9705883 32.5714285,78.7058821 L31.6190475,78.6176467 C30.7619047,78.5294113 30.0952381,78.352941 30.0952381,77.8235294 C30.0952381,77.2058821 30.7619047,76.8529409 31.8095238,76.8529409 C32.9523809,76.8529409 34.095238,77.2941175 34.6666665,77.5588232 L35.5238095,76.0588232 L35.5238095,76.0588232 Z M88.6666667,75.1764701 C87.5238096,75.1764701 86.7619049,75.705882 86.2857143,76.4117648 L86.2857143,75.3529409 L84.2857143,75.3529409 L84.2857143,83.8235292 L86.2857143,83.8235292 L86.2857143,79.0588233 C86.2857143,77.6470586 86.9523809,76.8529409 88.1904762,76.8529409 C88.5714285,76.8529409 89.0476191,76.9411763 89.4285715,77.0294113 L90.0000002,75.2647055 C89.6190479,75.1764701 89.0476191,75.1764701 88.6666667,75.1764701 L88.6666667,75.1764701 L88.6666667,75.1764701 Z M63.1428572,79.5882348 C63.1428572,82.1470584 65.0476191,84 68.0000002,84 C69.3333333,84 70.2857144,83.7352938 71.2380952,83.0294115 L70.2857144,81.5294114 C69.5238097,82.0588234 68.761905,82.3235292 67.904762,82.3235292 C66.2857144,82.3235292 65.1428572,81.2647057 65.1428572,79.5882348 C65.1428572,77.9999998 66.2857144,76.9411763 67.904762,76.8529409 C68.761905,76.8529409 69.5238097,77.1176467 70.2857144,77.6470586 L71.2380952,76.1470586 C70.2857144,75.4411763 69.3333333,75.1764701 68.0000002,75.1764701 C65.0476191,75.1764701 63.1428572,77.0294113 63.1428572,79.5882348 L63.1428572,79.5882348 L63.1428572,79.5882348 Z M81.6190477,79.5882348 L81.6190477,75.3529409 L79.6190477,75.3529409 L79.6190477,76.4117648 C78.9523811,75.6176466 78,75.1764701 76.7619047,75.1764701 C74.1904763,75.1764701 72.1904763,77.0294113 72.1904763,79.5882348 C72.1904763,82.1470584 74.1904763,84 76.7619047,84 C78.0952382,84 79.0476193,83.558823 79.6190477,82.7647057 L79.6190477,83.8235292 L81.6190477,83.8235292 L81.6190477,79.5882348 Z M74.2857145,79.5882348 C74.2857145,78.0882348 75.3333334,76.8529409 77.0476193,76.8529409 C78.666667,76.8529409 79.8095241,77.9999998 79.8095241,79.5882348 C79.8095241,81.0882349 78.666667,82.3235292 77.0476193,82.3235292 C75.3333334,82.2352938 74.2857145,81.0882349 74.2857145,79.5882348 L74.2857145,79.5882348 Z M50.3809523,75.1764701 C47.7142857,75.1764701 45.8095238,76.9411763 45.8095238,79.5882348 C45.8095238,82.2352938 47.7142857,84 50.4761905,84 C51.8095238,84 53.142857,83.6470584 54.1904759,82.8529411 L53.2380952,81.5294114 C52.4761905,82.0588234 51.5238094,82.4117646 50.5714287,82.4117646 C49.3333334,82.4117646 48.0952381,81.882353 47.8095238,80.382353 L54.5714283,80.382353 L54.5714283,79.6764701 C54.6666665,76.9411763 52.9523806,75.1764701 50.3809523,75.1764701 L50.3809523,75.1764701 L50.3809523,75.1764701 Z M50.3809523,76.7647055 C51.6190476,76.7647055 52.4761905,77.4705883 52.6666665,78.7941175 L47.9047621,78.7941175 C48.0952381,77.6470586 48.952381,76.7647055 50.3809523,76.7647055 L50.3809523,76.7647055 Z M100,79.5882348 L100,72 L98,72 L98,76.4117648 C97.3333334,75.6176466 96.3809523,75.1764701 95.1428574,75.1764701 C92.5714286,75.1764701 90.5714286,77.0294113 90.5714286,79.5882348 C90.5714286,82.1470584 92.5714286,84 95.1428574,84 C96.4761905,84 97.4285716,83.558823 98,82.7647057 L98,83.8235292 L100,83.8235292 L100,79.5882348 Z M92.6666668,79.5882348 C92.6666668,78.0882348 93.7142857,76.8529409 95.4285716,76.8529409 C97.0476193,76.8529409 98.1904764,77.9999998 98.1904764,79.5882348 C98.1904764,81.0882349 97.0476193,82.3235292 95.4285716,82.3235292 C93.7142857,82.2352938 92.6666668,81.0882349 92.6666668,79.5882348 L92.6666668,79.5882348 Z M25.8095239,79.5882348 L25.8095239,75.3529409 L23.8095239,75.3529409 L23.8095239,76.4117648 C23.1428571,75.6176466 22.1904762,75.1764701 20.9523809,75.1764701 C18.3809522,75.1764701 16.3809522,77.0294113 16.3809522,79.5882348 C16.3809522,82.1470584 18.3809522,84 20.9523809,84 C22.2857142,84 23.2380953,83.558823 23.8095239,82.7647057 L23.8095239,83.8235292 L25.8095239,83.8235292 L25.8095239,79.5882348 Z M18.3809523,79.5882348 C18.3809523,78.0882348 19.4285714,76.8529409 21.1428571,76.8529409 C22.7619047,76.8529409 23.9047618,77.9999998 23.9047618,79.5882348 C23.9047618,81.0882349 22.7619047,82.3235292 21.1428571,82.3235292 C19.4285714,82.2352938 18.3809523,81.0882349 18.3809523,79.5882348 Z"
                id="Shape"
                fill="#FFFFFF"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
}

export function IconVisa({ className }) {
  return (
    <svg
      className={className}
      width="75px"
      height="45px"
      viewBox="0 0 200 120"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <title>visa</title>
      <g
        id="Rounded"
        stroke="none"
        strokeWidth="1"
        fill="none"
        fillRule="evenodd"
      >
        <g id="Large" transform="translate(-32.000000, -28.000000)">
          <g id="large/visa" transform="translate(32.000000, 28.000000)">
            <rect
              id="Background"
              fill="#25459A"
              x="0"
              y="0"
              width="200"
              height="120"
              rx="8"
            ></rect>
            <g
              id="Logo"
              transform="translate(32.000000, 40.000000)"
              fill="#FFFFFF"
              fillRule="nonzero"
            >
              <path
                d="M68.2672516,27.2122001 C68.1919404,21.6403198 73.5588253,18.5307792 77.601826,16.6821648 C81.7558108,14.7851962 83.151042,13.5689046 83.1351871,11.8727915 C83.1034772,9.27654823 79.8215122,8.130928 76.7496247,8.08629345 C71.3906676,8.00818295 68.2751783,9.44392782 65.7978502,10.5300353 L63.8675158,2.05318951 C66.3527715,0.97824065 70.9546576,0.0409150083 75.7269836,0 C86.9284726,0 94.2574019,5.18876693 94.2970395,13.2341454 C94.3406403,23.4442997 79.2467723,24.0096708 79.3498288,28.5735539 C79.3855023,29.9572251 80.7926254,31.4338851 83.8764037,31.8095591 C85.4024382,31.9992559 89.6158792,32.1443183 94.3921687,30.0799701 L96.2670112,38.2815696 C93.6985165,39.1593824 90.3967329,40 86.2863494,40 C75.7428385,40 68.3267073,34.7405617 68.2672516,27.2122001 M114.282145,39.2932862 C112.236863,39.2932862 110.512642,38.1737028 109.743679,36.4552724 L93.7421172,0.602566485 L104.93568,0.602566485 L107.163294,6.37902176 L120.842112,6.37902176 L122.134286,0.602566485 L132,0.602566485 L123.390787,39.2932862 L114.282145,39.2932862 M115.847817,28.8413614 L119.078253,14.3128138 L110.231217,14.3128138 L115.847817,28.8413614 M54.6954535,39.2932862 L45.8721999,0.602566485 L56.538586,0.602566485 L65.3578762,39.2932862 L54.6954535,39.2932862 M38.915861,39.2932862 L27.8134648,12.958899 L23.3225632,35.3505672 C22.7953877,37.8501021 20.7144316,39.2932862 18.4035794,39.2932862 L0.253678458,39.2932862 L0,38.1699831 C3.72590236,37.4111957 7.95916162,36.187465 10.5236923,34.8781847 C12.0933277,34.0784823 12.5412287,33.3792076 13.0565131,31.4785195 L21.5626689,0.602566485 L32.8355053,0.602566485 L50.1173503,39.2932862 L38.915861,39.2932862"
                id="Shape"
                transform="translate(66.000000, 20.000000) scale(-1, 1) rotate(-180.000000) translate(-66.000000, -20.000000) "
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
}
