import { createSlice } from "@reduxjs/toolkit";

export const applicationSlice = createSlice({
  name: "applicationForm",
  initialState: {
    requirements: [],
    countryId: 0,
    preconditionData:{
      countryId:0,
      passportTypeId:0,
      visaTypeId:0,
      precondition: null
    },
    investigation: {
      id: 0,
      members: [],
      currentMemberId: 0,
      status:null,
      investigationTypeId:0
    }
  },
  
  reducers: {
    setRequirements: (state, action) => {
      state.requirements = action.payload;
    },

    setCountry: (state, action) => {
      state.countryId = action.payload;
    },

    setPrecondition: (state, action) => {
      state.preconditionData = action.payload;
    },

    setInvestigation: (state, action) => {
      state.investigation.id = action.payload.id;
      state.investigation.currentMemberId = action.payload.currentMemberId;
      state.investigation.status =  action.payload.status,
      state.investigation.investigationTypeId =  action.payload.investigationTypeId
    },

    setInvestigationMember: (state, action) => {
      var index = state.investigation.members?.findIndex((e, i) => {
        return e.investigationMemberId == action.payload.investigationMemberId;
      });

      if (index > -1) {
        var oldmember = state.investigation.members.find((e) => {
          return e.investigationMemberId == action.payload.investigationMemberId;
        });

        if (oldmember) {
          action.payload.files = oldmember.files;
        }
        state.investigation.members[index] = action.payload;
      } else {
        state.investigation.members.push(action.payload);
      }
    },

    setMemberFiles: (state, action) => {
      var index = state.investigation.members.findIndex((e, i) => {
        return e.investigationMemberId == action.payload.investigationMemberId;
      });

      if(index > -1){
        state.investigation.members[index].files = action.payload.files;
      }
    },

    clearApplication: (state, action) => {
      state.requirements = [];
      state.countryId = 0;
      state.investigation = {
        id: 0,
        members: [],
        currentMemberId: 0,
      };
      state.preconditionData ={
        countryId:0,
        passportTypeId:0,
        visaTypeId:0,
        precondition: null
      };
    }
  },
});

export const {
  setRequirements,
  setCountry,
  setInvestigation,
  setInvestigationMember,
  setMemberFiles,
  clearApplication,
  setInvestigationType,
  setPrecondition
} = applicationSlice.actions;

export default applicationSlice.reducer;
