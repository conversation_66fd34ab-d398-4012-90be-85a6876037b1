import {
  EnvironmentOutlined,
  AccountBookOutlined,
  FormOutlined,
  PhoneOutlined,
  QuestionCircleOutlined,
  PlusCircleOutlined,
  SisternodeOutlined,
  UserOutlined,
  SolutionOutlined,
  PoweroffOutlined,
} from "@ant-design/icons";
import {
  Layout,
  Menu,
  theme,
  Button,
  Space,
} from "antd";
import { useRouter } from "next/router";
import { useState } from "react";
import "dayjs/locale/tr";
import localeTR from "antd/locale/tr_TR";
import localeEN from "antd/locale/en_US";
import { useSession, signIn } from "next-auth/react";
import Dropdown from "react-bootstrap/Dropdown";
import Link from "next/link";

const { Header, Content, Sider } = Layout;
function getItem(label, key, icon, children, path) {
  return {
    key,
    icon,
    children,
    label,
    path,
  };
}
const autItems = [
  getItem("Home", "/", <AccountBookOutlined />),
  getItem("Application", "sub1", <FormOutlined />, [
    getItem(
      "New Application",
      "/application/new-application",
      <PlusCircleOutlined />
    ),
    getItem(
      "My Applications",
      "/application/my-applications",
      <SisternodeOutlined />
    ),
  ]),
  getItem("Feedback", "/feedback", <QuestionCircleOutlined />),
  getItem("Call Center", "/call-center", <PhoneOutlined />),
  getItem("Agencies", "/agency", <EnvironmentOutlined />),
  getItem("Forms", "/forms", <FormOutlined />),
];

const publicItems = [
  getItem("Dashboard", "/", <AccountBookOutlined />),
  getItem("Forms", "/forms", <FormOutlined />),
];

const App = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [locale, setLocale] = useState(localeEN);
  const route = useRouter();
  const { data: session, status } = useSession();
  const {
    token: { colorBgContainer },
  } = theme.useToken();

  const onClick = (e) => {
    route.push(e.key);
  };

  function dChange(e) {
    localStorage.setItem("locale", e);
    setLocale(localeTR);
    route.push("", "", { locale: e });
  }

  const SSOLogout = () => {
    if (session == null) {
      return res.redirect("/");
    }

    const endSessionURL = `${process.env.IdentityServer4_Issuer}/connect/endsession`;
    const redirectURL = `${process.env.NEXTAUTH_URL}/logout/`;
    const token = session.idToken;
    const fullUrl = `${endSessionURL}?id_token_hint=${token}&post_logout_redirect_uri=${redirectURL}`;

    window.location.replace(fullUrl);
  };

  return (
    <>
      <Layout
        style={{
          minHeight: "100vh",
        }}
      >
        <Header
          style={{
            background: "#4C1D95",
            height: 70,
          }}
        >
          <div className="row">
            <div
              style={{
                height: 70,
                paddingLeft: 20,
                paddingTop: 10,
                background: "#4C1D95",
              }}
              className="col-3"
            >
              <Link href="/" style={{ textDecoration: "none" }}>
                <p className="text-white logo-title m-0">E-Visa</p>
                <p className="logo-subtitle">Türkiye</p>
              </Link>
            </div>
            <div className=" col-9 text-end">
              <Space>
                <Dropdown>
                  <Dropdown.Toggle
                    style={{ backgroundColor: "transparent" }}
                    className="border-0"
                    id="dropdown-basic"
                  >
                    {route.locale.toUpperCase()}
                  </Dropdown.Toggle>

                  <Dropdown.Menu >
                    <Dropdown.Item>
                      <Button
                        type="link"
                        onClick={() => {
                          dChange("en");
                        }}
                        style={{ color: "black" }}
                      >
                        English
                      </Button>
                    </Dropdown.Item>
                    <Dropdown.Item>
                      <Button
                        type="link"
                        onClick={() => {
                          dChange("tr");
                        }}
                        style={{ color: "black" }}
                      >
                        Türkçe
                      </Button>
                    </Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown>

                {!session && (
                  <>
                    <Button
                      type="primary"
                      onClick={() => {
                        signIn("identity-server4");
                      }}
                      style={{ marginRight: 30 }}
                    >
                      Login
                    </Button>
                  </>
                )}

                {session && (
                  <Dropdown>
                    <Dropdown.Toggle
                      style={{ backgroundColor: "transparent" }}
                      className="border-0"
                      id="dropdown-basic"
                    >
                      <UserOutlined />
                    </Dropdown.Toggle>

                    <Dropdown.Menu>
                      <Dropdown.Item
                        className="py-1"
                        style={{ maxHeight: "50px" }}
                      >
                        <label>{session?.user?.email}</label>
                      </Dropdown.Item>
                      <Dropdown.Divider />
                      <Dropdown.Item>
                        <SolutionOutlined className="icon" />
                        <Button type="link" style={{ color: "black" }}>
                          Profile
                        </Button>
                      </Dropdown.Item>
                      <Dropdown.Item>
                        <PoweroffOutlined
                          className="icon"
                          style={{ color: "red" }}
                        />
                        <Button
                          type="link"
                          onClick={() => {
                            SSOLogout();
                          }}
                          danger
                        >
                          Sign Out
                        </Button>
                      </Dropdown.Item>
                    </Dropdown.Menu>
                  </Dropdown>
                )}
              </Space>
            </div>
          </div>
        </Header>
        <Layout className="site-layout">
          <Sider
            collapsible
            collapsed={collapsed}
            theme="light"
            onCollapse={(value) => setCollapsed(value)}
          >
            <Menu
              onClick={onClick}
              theme="light"
              defaultSelectedKeys={["1"]}
              mode="inline"
              items={status == "authenticated" ? autItems : publicItems}
            ></Menu>
          </Sider>
          <Content
            style={{
              margin: "0",
            }}
          >
            <div
              style={{
                padding: 0,
                minHeight: 360,
                width: "100%",
                height: "100%",
                backgroundImage: "url(/img/evisa/gradient.svg)",
                backgroundSize: "cover",
              }}
            >
              {children}
            </div>
          </Content>
        </Layout>
      </Layout>
    </>
  );
};
export default App;
