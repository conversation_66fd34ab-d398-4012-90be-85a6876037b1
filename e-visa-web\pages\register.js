import React from "react";
import { Button, Form, Input, Checkbox } from "antd";
import { toast } from "react-toastify";
import { useRouter } from "next/router";
import { useRef, useEffect, useState } from "react";
import useTranslation from "next-translate/useTranslation";
import Link from "next/link";
import axioshelper, { BaseUrlType, MethodType } from "../pages/api/axioshelper";
import UserEnter from "../components/userEnter";
// import ReCAPTCHA from "react-google-recaptcha";

const Register = () => {
  const [loadingAction, setLoadingAction] = useState(false);
  const [disabledAction, setDisabledAction] = useState(false);
  const [reCaptchaToken, setReCaptchaToken] = useState(null);
  const [form] = Form.useForm();
  const { t } = useTranslation();
  var router = useRouter();
  const inputRef = useRef(null);

  useEffect(() => {
    setTimeout(() => {
      inputRef.current?.focus();
    }, 10);
  }, []);

  const onOk = () => {
    form
      .validateFields()
      .then(async (values) => {
        setLoadingAction(true);
        const result = await axioshelper(
          MethodType.POST,
          BaseUrlType.Public_API,
          `register`,
          values
        );

        setLoadingAction(false);
        if (result?.status == "SUCCESS") {
          toast.success(t("site:registerSuccess"));
          router.push("/login");
        }
        else{
          toast.error(result?.data?.message)
        }
      })
      .catch(() => {});
  };

  // const reCaptchaOnChange = (value) => {
  //   //-- süre aşımında null geliyor butonu kilitliyoruz ki ilerleyemesin
  //   if (value === null) {
  //     setDisabledAction(true);
  //   } else {
  //     //-- içi doluysa token geldi olarak varsayıyoruz bu kodu BE de doğrulayacağız
  //     setDisabledAction(false);
  //   }
  //   setReCaptchaToken(value);
  // };

  return (
    <UserEnter>
      <h1>{t("site:welcome")}</h1>
      <p className="right-body-welcome">{t("site:registerEVisa")}</p>
      <Form form={form} layout="vertical">
        <div className="form-group-2">
          <Form.Item
            className="form-group flex-auto d-block"
            label={t("form:name")}
            name="name"
            rules={[
              {
                required: true,
                max: 50,
              },
            ]}
          >
            <Input
              ref={inputRef}
              className="form-control"
              maxLength={50}
              onPressEnter={() =>
                form
                  .validateFields()
                  .then(() => {
                    onOk();
                  })
                  .catch(() => {})
              }
            />
          </Form.Item>
          <Form.Item
            className="form-group flex-auto d-block"
            label={t("form:surname")}
            name="surname"
            rules={[
              {
                required: true,
                max: 50,
              },
            ]}
          >
            <Input
              className="form-control"
              maxLength={50}
              onPressEnter={() =>
                form
                  .validateFields()
                  .then(() => {
                    onOk();
                  })
                  .catch(() => {})
              }
            />
          </Form.Item>
        </div>
        <Form.Item
          className="form-group"
          label={t("site:email")}
          name="email"
          rules={[
            {
              required: true,
              type: "email"
            },
          ]}
        >
          <Input
            className="form-control"
            maxLength={50}
            onPressEnter={() =>
              form
                .validateFields()
                .then(() => {
                  onOk();
                })
                .catch(() => {})
            }
          />
        </Form.Item>
        <Form.Item
          className="form-group"
          label={t("form:password")}
          name="password"
          rules={[
            {
              required: true,
              max: 20,
            },
          ]}
        >
          <Input.Password
            className="form-control-password"
            maxLength={20}
            onPressEnter={() =>
              form
                .validateFields()
                .then(() => {
                  onOk();
                })
                .catch(() => {})
            }
          />
        </Form.Item>
        <Form.Item className="form-group" label={t("form:phone")} name="tel">
          <Input
            className="form-control"
            maxLength={20}
            onPressEnter={() =>
              form
                .validateFields()
                .then(() => {
                  onOk();
                })
                .catch(() => {})
            }
          />
        </Form.Item>
        <Form.Item
          name="KVKK"
          valuePropName="checked"
          className="mt-4"
          rules={[
            {
              required: true,
              transform: (value) => value || undefined,
              type: "boolean",
              message: `${t("site:approvalRequired")}`,
            },
          ]}
        >
          <Checkbox>
            <Link
              target="_blank"
              locale={false}
              href={`/files/kvkk_${router.locale}.pdf`}
            >
              {t("site:pdplClarificationText")}
            </Link>{" "}
            {t("site:pdplReadAndUnderstood")}
          </Checkbox>
        </Form.Item>
        {/* <ReCAPTCHA
          className="slf-recaptcha"
          hl={router.locale}
          sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY}
          onChange={reCaptchaOnChange}
        /> */}
        <Button
          type="primary"
          className="btn-submit"
          onClick={() => onOk()}
          loading={loadingAction}
          disabled={disabledAction}
        >
          {t("form:signup")}
        </Button>
      </Form>
      <p className="right-body-register mt-24">
        {t("site:registerInfo1")}{" "}
        <Link href="/login" className="right-body-register-link">
          {t("site:loginText")}
        </Link>{" "}
        {t("site:registerInfo2")}
      </p>
    </UserEnter>
  );
};

export default Register;
