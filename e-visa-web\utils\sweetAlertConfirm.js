import React from "react";
import Swal from "sweetalert2";
import withReactContent from "sweetalert2-react-content";
import 'animate.css';
import Router from "next/router";

const showSweetAlertConfirm = (title, text, icon, timer = 0) => {
  // const { t } = useTranslation();
  const MySwal = withReactContent(Swal);
  if(timer == 0){
    return MySwal.fire({
      title: <strong>{title}</strong>,
      html: <i>{text}</i>,
      icon: icon,
      showDenyButton: true,
      confirmButtonText: Router.locale == "tr" ? "Evet" : "Yes",
      denyButtonText: Router.locale == "tr" ? "Hayır" : "No",
      showClass: {
        popup: 'animate__animated animate__fadeInDown'
      },
      hideClass: {
        popup: 'animate__animated animate__fadeOutUp'
      }
    }).then((result) => {
      return result;
    });
  }
  else{
    return MySwal.fire({
      title: <strong>{title}</strong>,
      html: <i>{text}</i>,
      icon: icon,
      showDenyButton: true,
      confirmButtonText: Router.locale == "tr" ? "Evet" : "Yes",
      denyButtonText: Router.locale == "tr" ? "Hayır" : "No",
      timer:timer,
      timerProgressBar: true,
      showClass: {
        popup: 'animate__animated animate__fadeInDown'
      },
      hideClass: {
        popup: 'animate__animated animate__fadeOutUp'
      }
    }).then((result) => {
      return result;
    });
  }

};

export default showSweetAlertConfirm;
