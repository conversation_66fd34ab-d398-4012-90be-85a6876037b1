import ModalLayout from "../../layouts/ModalLayout";
import { useEffect } from "react";
import axioshelper, {
  BaseUrlType,
  MethodType,
} from "../../pages/api/axioshelper";

export async function getServerSideProps(context) {
  return {
    props: {
      orderId: context?.query?.orderId ?? null,
      investigationId: context?.query?.externalOrderNumber ?? null,
      status: context?.query?.status,
    },
  };
}

const PaymentResult = ({ orderId, investigationId, status }) => {

  useEffect(() => {
    if (status == "Successful") {
      completeInvestigation();
    } else {
      localStorage.setItem("paymentStatus", "false");
    }
  }, []);

  const completeInvestigation = async () => {
    await axioshelper(
      MethodType.POST,
      BaseUrlType.Public_API,
      `investigations/${investigationId}/complete`,
      { orderId }
    );
    localStorage.setItem("paymentStatus", "true");
  };
  return <></>;
};

PaymentResult.PageLayout = ModalLayout;
export default PaymentResult;
