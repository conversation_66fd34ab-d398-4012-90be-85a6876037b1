import React from "react";
import { Button, Form, Input } from "antd";
import { signIn } from "next-auth/react";
import { toast } from "react-toastify";
import { useRouter } from "next/router";
import { useRef, useEffect, useState } from "react";
import useTranslation from "next-translate/useTranslation";
import Link from "next/link";
import UserEnter from "../components/userEnter";
// import ReCAPTCHA from "react-google-recaptcha";

const Login = () => {
  const [loadingAction, setLoadingAction] = useState(false);
  // NOTE - recaptcha işi geri geldiğinde düzelecektir.
  const [disabledAction, setDisabledAction] = useState(false);
  const [reCaptchaToken, setReCaptchaToken] = useState(null);
  const [form] = Form.useForm();
  const { t } = useTranslation();
  var router = useRouter();
  const inputRef = useRef(null);

  useEffect(() => {
    setTimeout(() => {
      inputRef.current?.focus();
    }, 10);
  }, []);

  const onOk = () => {
    form
      .validateFields()
      .then(async (values) => {
        setLoadingAction(true);
        const loginRes = await signIn("is-credential", {
          redirect: false,
          username: values.username,
          password: values.password,
          reCaptchaToken: reCaptchaToken,
        });
        setLoadingAction(false);
        if (loginRes?.ok) {
          window.location.href = "/";
        } else {
          toast.warning(t("site:loginError"));
        }
      })
      .catch(() => {});
  };

  // NOTE - recaptcha işi geri geldiğinde eklenecektir.
  // const reCaptchaOnChange = (value) => {
  //   //-- süre aşımında null geliyor butonu kilitliyoruz ki ilerleyemesin
  //   if (value === null) {
  //     setDisabledAction(true);
  //   } else {
  //     //-- içi doluysa token geldi olarak varsayıyoruz bu kodu BE de doğrulayacağız
  //     setDisabledAction(false);
  //   }
  //   setReCaptchaToken(value);
  // };

  return (
    <UserEnter>
      <h1>{t("site:welcome")}</h1>
      <p className="right-body-welcome">{t("site:signInEVisa")}</p>
      <Form form={form} layout="vertical">
        <Form.Item
          className="form-group"
          label={t("site:email")}
          name="username"
          rules={[
            {
              required: true,
              type: "email",
            },
          ]}
        >
          <Input
            ref={inputRef}
            className="form-control"
            maxLength={50}
            onPressEnter={() =>
              form
                .validateFields()
                .then(() => {
                  onOk();
                })
                .catch(() => {})
            }
          />
        </Form.Item>
        <Form.Item
          className="form-group"
          label={t("form:password")}
          name="password"
          rules={[
            {
              required: true,
            },
          ]}
        >
          <Input.Password
            className="form-control-password"
            maxLength={20}
            onPressEnter={() =>
              form
                .validateFields()
                .then(() => {
                  onOk();
                })
                .catch(() => {})
            }
          />
        </Form.Item>
        {/* 
  // NOTE - recaptcha işi geri geldiğinde eklenecektir.
        <ReCAPTCHA
          className="slf-recaptcha"
          hl={router.locale}
          sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY}
          onChange={reCaptchaOnChange}
        /> */}
        <Button
          type="primary"
          className="btn-submit"
          onClick={() => onOk()}
          loading={loadingAction}
          disabled={disabledAction}
        >
          {t("form:signin")}
        </Button>
      </Form>
      <Link href="/forgot-password" className="right-body-reset-password">
        {t("site:forgotPassword")}
      </Link>
      <hr />
      <p className="right-body-register">
        {t("site:registerDesc")}{" "}
        <Link href="/register" className="right-body-register-link">
          {t("site:register")}
        </Link>
      </p>
    </UserEnter>
  );
};
export default Login;
