import { Button, Form, Input, Select } from "antd";
import { useSession } from "next-auth/react";
import useTranslation from "next-translate/useTranslation";
import axioshelper, { BaseUrlType, MethodType } from "../api/axioshelper";
import api from "../api/helpers";
import useStateRef from "react-usestateref";
import { useEffect } from "react";
import showSweetAlert from "../../utils/sweetAlert";
import { useRouter } from "next/router";

const { TextArea } = Input;
const FeedBack = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const { data: session, status } = useSession();
  const [feedbackTypes, setFeedbackTypes, feedbackTypesRef] = useStateRef([]);

  const onFinish = async (model) => {
    var resultInvestigation = await axioshelper(
      MethodType.POST,
      BaseUrlType.Public_API,
      `feedbacks`,
      model
    );

    if (resultInvestigation.status == "SUCCESS") {
      showSweetAlert(
        t("site:success"),
        t("site:successMessagev2"),
        "success"
      ).then(() => {
        router.push("/");
      });
    }
  };

  useEffect(() => {
    if (status == "authenticated") {
      api.GetDefinitions("feedback-type").then((result) => {
        if (result != null && result?.status == "SUCCESS") {
          setFeedbackTypes(result.data);
        }
      });
    }
  }, [status, router.locale]);

  return (
    <div className="container slf-container">
      <div className="row pt-4">
        <div className="col">
          <h3>{t("site:feedback")}</h3>
          <p className="fst-italic">{t("site:feedBackDescription")}</p>
        </div>
        <div className="col-12">
          <Form
            name="frm-feedback"
            labelCol={{
              span: 12,
            }}
            size="large"
            onFinish={onFinish}
            wrapperCol={{
              span: 32,
            }}
            layout="vertical"
          >
            <div className="row">
              <div className="col-lg-6 col-md-6 col-sm-12">
                <Form.Item
                  name={["feedbackType"]}
                  label={t("form:feedbackType")}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    showSearch
                    size="large"
                    style={{ width: "100%" }}
                    placeholder={t("form:feedbackType")}
                    options={feedbackTypesRef?.current.map((feedback) => {
                      return {
                        value: feedback.code,
                        label: feedback.explanation,
                      };
                    })}
                  />
                </Form.Item>
              </div>
              <div className="col-lg-6 col-md-6 col-sm-12">
                <Form.Item
                  name={["email"]}
                  label={t("form:email")}
                  rules={[
                    {
                      required: true,
                      max: 200,
                    },
                    { type: "email" },
                  ]}
                >
                  <Input maxLength={200} />
                </Form.Item>
              </div>
            </div>
            <Form.Item
              name={["subject"]}
              label={t("form:subject")}
              rules={[
                {
                  required: true,
                  max: 100,
                },
              ]}
            >
              <Input maxLength={100} />
            </Form.Item>
            <Form.Item
              name={["message"]}
              label={t("form:message")}
              rules={[
                {
                  required: true,
                  max: 1000,
                },
              ]}
            >
              <TextArea maxLength={1000} rows={4} />
            </Form.Item>
            <div className="row mt-3">
              <div className="col-6"></div>
              <div className="col-6 text-end">
                <Button
                  size="large"
                  htmlType="submit"
                  className="btn stepper-button-next"
                >
                  {t("site:send")}
                </Button>
              </div>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default FeedBack;
